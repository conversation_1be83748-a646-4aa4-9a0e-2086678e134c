ARG ALPINE_VERSION=3.19
FROM alpine:${ALPINE_VERSION} AS core

# Setup document root
WORKDIR /var/www/html

# Install packages and remove default server definition
RUN apk add --no-cache \
  curl \
  nginx \
  npm \
  php83 \
  php83-bcmath \
  php83-ctype \
  php83-curl \
  php83-cli \
  php83-dom \
  php83-exif \
  php83-fileinfo \
  php83-fpm \
  php83-gd \
  php83-intl \
  php83-mbstring \
  php83-mysqli \
  php83-pdo_mysql \
  php83-opcache \
  php83-openssl \
  php83-phar \
  php83-pecl-redis \
  php83-pcntl \
  php83-posix \
  php83-session \
  php83-simplexml \
  php83-tokenizer \
  php83-xml \
  php83-xmlreader \
  php83-xmlwriter \
  php83-zip \
  supervisor

# Configure nginx - http
COPY .docker/etc/nginx/nginx.conf /etc/nginx/nginx.conf
# Configure nginx - default server
COPY .docker/etc/nginx/conf.d /etc/nginx/conf.d/

# Configure PHP-FPM
ENV PHP_INI_DIR /etc/php83
COPY .docker/etc/php/php-fpm.d/www.conf ${PHP_INI_DIR}/php-fpm.d/www.conf
COPY .docker/etc/php/conf.d/custom.ini ${PHP_INI_DIR}/conf.d/custom.ini

COPY --from=composer /usr/bin/composer /usr/bin/composer

# Configure supervisord
COPY .docker/etc/supervisor/conf.d/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Create user
ENV USER=sail
ENV GROUPNAME=$USER
ENV UID=1000
ENV GID=1000
RUN addgroup \
    --gid "$GID" \
    "$GROUPNAME" \
&&  adduser \
    --disabled-password \
    --gecos "" \
    --home "$(pwd)" \
    --ingroup "$GROUPNAME" \
    --no-create-home \
    --uid "$UID" \
    $USER

# Make sure files/folders needed by the processes are accessable when they run under the nobody user
RUN chown -R "$USER"."$USER" /var/www/html /run /var/lib/nginx /var/log/nginx

# Create symlink for php
RUN ln -s /usr/bin/php83 /usr/bin/php

# Create necessary folders
RUN mkdir -p /.composer
RUN chown -R "$USER":"$USER" /.composer
RUN mkdir -p /.npm
RUN chown -R "$USER":"$USER" /.npm

# Switch to use a non-root user from here on
USER "$USER"

# install php packages
COPY --chown="$USER" composer.json /var/www/html/
COPY --chown="$USER" composer.lock /var/www/html/

ARG COMPOSER_WE_USER
ARG COMPOSER_WE_PASS
ENV COMPOSER_WE_USER=${COMPOSER_WE_USER}
ENV COMPOSER_WE_PASS=${COMPOSER_WE_PASS}
RUN composer config http-basic.wire-elements-pro.composer.sh "${COMPOSER_WE_USER}" "${COMPOSER_WE_PASS}"


RUN composer install \
  --no-autoloader \
  --no-interaction \
  --no-progress

# install php packages
COPY --chown="$USER" package.json /var/www/html/
COPY --chown="$USER" package-lock.json /var/www/html/

RUN npm install

# Add application
COPY --chown="$USER" . /var/www/html/

# Expose the port nginx is reachable on
EXPOSE 8080

# Let supervisord start nginx & php-fpm
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]

# Configure a healthcheck to validate that everything is up&running
HEALTHCHECK --timeout=10s CMD curl --silent --fail http://127.0.0.1:8080/fpm-ping || exit 1

FROM core AS dev

# Temporary switch to root
USER root

# Install xdebug
RUN apk add --no-cache php83-pecl-xdebug

# Add configuration
COPY .docker/etc/php/conf.d/xdebug.ini ${PHP_INI_DIR}/conf.d/xdebug.ini

# Switch back to non-root user
USER "$USER"

FROM core as prod

RUN composer install \
  --no-interaction \
  --no-progress

RUN npm run build
