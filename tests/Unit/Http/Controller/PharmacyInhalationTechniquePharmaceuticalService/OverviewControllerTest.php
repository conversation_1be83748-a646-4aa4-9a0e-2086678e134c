<?php

namespace Tests\Unit\Http\Controller\PharmacyInhalationTechniquePharmaceuticalService;

use App\Actions\AnonymizePharmaceuticalServiceAction;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\PharmaceuticalService\PharmaceuticalServiceTypeEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OverviewControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_anonymize_pharmaceutical_service_action_is_called(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $pharmacy->refresh();

        $pharmaceuticalService = $pharmacy->pharmaceuticalServices()->create([
            'association_id' => $pharmacy->owner()?->pharmacyProfile?->association_id,
            'user_id' => $user->id,
            'date' => now(),
            'type' => PharmaceuticalServiceTypeEnum::MEASURE_BLOOD_PRESSURE,
        ]);
        $pharmaceuticalService->pharmaceuticalServicePatient()->create();
        $pharmaceuticalService->measureBloodPressurePatient()->create();

        $this->mock(AnonymizePharmaceuticalServiceAction::class, function ($mock) {
            $mock->shouldReceive('execute')
                ->once();
        });

        // Act
        $response = $this->actingAs($user)->post(route('pharmacies.pharmaceutical-services.measure-blood-pressures.overview', [
            'pharmacy' => $pharmacy,
            'pharmaceuticalService' => $pharmaceuticalService,
        ]))->assertRedirectToRoute('pharmacies.pharmaceutical-services.measure-blood-pressures.overview', [$pharmacy, $pharmaceuticalService]);
    }
}
