{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "repositories": [{"type": "composer", "url": "https://wire-elements-pro.composer.sh"}], "require": {"php": "^8.2", "ext-intl": "*", "bradietilley/country-enums": "^1.5", "cknow/laravel-money": "^8.1", "coderflex/laravel-turnstile": "^2.0", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "filament/spatie-laravel-tags-plugin": "^3.2", "laravel/framework": "^11.0", "laravel/horizon": "^5.31", "laravel/reverb": "^1.4", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.14", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "livewire/livewire": "^3.4", "livewire/volt": "^1.0", "log1x/laravel-webfonts": "^1.0", "lorisleiva/laravel-actions": "^2.8", "mallardduck/blade-lucide-icons": "^1.23", "pelmered/filament-money-field": "^1.3", "ryangjchandler/laravel-cloudflare-turnstile": "^1.1", "spatie/laravel-medialibrary": "^11.5", "spatie/laravel-tags": "^4.6", "spatie/livewire-filepond": "^1.3", "usernotnull/tall-toasts": "^2.1", "webbingbrasil/filament-copyactions": "^3.0", "wire-elements/modal": "^2.0", "wire-elements/pro": "^4.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "barryvdh/laravel-ide-helper": "^3.1", "fakerphp/faker": "^1.23", "laravel/breeze": "^2.0", "laravel/pint": "^1.17", "laravel/sail": "^1.43", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "Illuminate\\Foundation\\ComposerScripts::postUpdate", "@composer ide-helper"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "ide-helper": ["@php artisan ide-helper:generate", "@php artisan ide-helper:meta", "@php artisan ide-helper:models -N --reset"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}