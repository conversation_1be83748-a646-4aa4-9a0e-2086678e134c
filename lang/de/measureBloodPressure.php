<?php

use App\Enums\Vaccinate\InformationPlacesEnum;
use App\Enums\Vaccinate\ReasonsForPharmacyEnum;
use App\Enums\Vaccinate\SevereVaccinationReactionsEnum;
use App\Enums\Vaccinate\VaccinationPlacesEnum;

return [
    'poll' => [
        'poll_vaccinated_before' => [
            VaccinationPlacesEnum::OTHER => 'Ja, bei sonstigen Stellen',
            VaccinationPlacesEnum::FAMILY_DOCTOR => 'Ja, beim (Haus-)Arzt',
            VaccinationPlacesEnum::COMPANY_DOCTOR => 'Ja, beim Betriebsarzt',
            VaccinationPlacesEnum::HEALTH_DEPARTMENT => 'Ja, im Gesundheitsamt',
            VaccinationPlacesEnum::MEDICAL_SPECIALIST => 'Ja, beim Facharzt',
            VaccinationPlacesEnum::NONE => 'Nein',
        ],
        'poll_where_found_out' => [
            //InformationPlacesEnum::DID_BEFORE => 'Schon einmal in Anspruch genommen',
            InformationPlacesEnum::PHARMACY_STAFF => 'Durch das Personal in der Apotheke',
            InformationPlacesEnum::INFORMATION_MATERIAL_OF_PHARMACY => 'Poster/Flyer in der Apotheke',
            InformationPlacesEnum::MEDICAL_OFFICE => 'In der Arztpraxis',
            InformationPlacesEnum::HEALTH_INSURANCE_COMPANY => 'Durch die Krankenkasse',
            InformationPlacesEnum::ADVERTISEMENT => 'Anzeige in einer Zeitschrift, Zeitung, Werbung',
            InformationPlacesEnum::RECOMMENDATION => 'Mundpropaganda',
            InformationPlacesEnum::INTERNET => 'Internet',
            InformationPlacesEnum::OTHER => 'Sonstiges',
        ],
        'poll_had_alternative' => [
            0 => 'Ja',
            1 => 'Nein',
            2 => 'Weiß nicht',
        ],
        'poll_why_pharmacy' => [
            ReasonsForPharmacyEnum::EASY_ACCESS => 'Leichte Erreichbarkeit der Apotheke',
            ReasonsForPharmacyEnum::TRUST_IN_COMPETENCE => 'Vertrauen in die Kompetenz des Apothekers/der Apothekerin und die Apotheke',
            ReasonsForPharmacyEnum::REGULAR_CUSTOMER => 'Stammkunde der Apotheke',
            ReasonsForPharmacyEnum::NO_WAITING_TIMES => 'Keine Wartezeiten',
            ReasonsForPharmacyEnum::FAVORABLE_OPENING_HOURS => 'Günstige Öffnungszeiten der Apotheke',
            ReasonsForPharmacyEnum::INFORMED_BY_PHARMACY => 'Ich wurde von der Apotheke auf die Impfmöglichkeit hingewiesen',
            ReasonsForPharmacyEnum::RECOMMENDED => 'Wurde mir von Bekannten/Freunden/Familie empfohlen',
            ReasonsForPharmacyEnum::NOT_OFFERED_BY_DOCTOR => 'Impfung wurde mir vom Arzt nicht angeboten',
            ReasonsForPharmacyEnum::WORRIED_BECAUSE_OF_CORONA => 'Angst vor Ansteckung mit dem Corona-Virus in der Arztpraxis',
            ReasonsForPharmacyEnum::PAY_SELF_AT_DOCTOR => 'Beim Arzt hätte ich die Impfung selbst bezahlen müssen',
            ReasonsForPharmacyEnum::OTHER => 'Sonstiges',
        ],
        'rating' => [
            0 => 'Sehr gut',
            1 => 'Gut',
            2 => 'Ausreichend',
            3 => 'Schlecht',
            4 => 'Weiß nicht',
        ],
        'probability' => [
            0 => 'Ja',
            1 => 'Wahrscheinlich',
            2 => 'Eher nein',
            3 => 'Nein',
            4 => 'Weiß nicht',
        ],
        'satisfaction' => [
            0 => 'Sehr zufrieden',
            1 => 'Ziemlich zufrieden',
            2 => 'Wenig zufrieden',
            3 => 'Gar nicht zufrieden',
            4 => 'Weiß nicht',
        ],
    ],
    'severe_vaccination_reaction' => [
        SevereVaccinationReactionsEnum::ANAPHYLACTIC_REACTION => 'Anaphylaktische Reaktion',
        SevereVaccinationReactionsEnum::EMERGENCY_SITUATION_CARDIOVASCULAR_SYSTEM => 'Notfallsituation Herz-Kreislauf-System',
        SevereVaccinationReactionsEnum::EMERGENCY_SITUATION_RESPIRATORY_SYSTEM => 'Notfallsituation respiratorisches System',
        SevereVaccinationReactionsEnum::NONE => 'Nein',
    ],
    'emergency_measures' => [
        0 => 'Nicht erforderlich',
        1 => 'Notruf',
        2 => 'Eigene Notfallbehandlung',
    ],

    'pharmacies_vaccinate_health_insurance_company' => 'Krankenversicherung',
    'pharmacies_vaccinate_personal_data' => 'Persönliche Daten',
    'pharmacies_vaccinate_educational' => 'Aufklärungsmerkblatt und Aufklärungsgespräch',
    'pharmacies_vaccinate_explanation' => 'Gesundheitliche Situation',
    'pharmacies_vaccinate_stiko' => 'STIKO',
    'pharmacies_vaccinate_acceptance' => 'Einverständniserklärung',
    'pharmacies_vaccinate_documentation' => 'Angaben zum Arzneimittel',
    'pharmacies_vaccinate_eval-show' => 'Evaluation der Angaben',
    'pharmacies_vaccinate_eval-user' => 'Fragen zur Impfung in der Apotheke',
    'pharmacies_vaccinate_eval-pharmacy' => 'Angaben zu Impfreaktionen',

    'vaccination_import' => [
        'type' => [
            \App\Enums\VaccinationImport\VaccinationImportTypeEnum::NORMALVACCINATION => 'COVID-19-Zertifikat',
            \App\Enums\VaccinationImport\VaccinationImportTypeEnum::RECOVERED => 'COVID-19-Genesenenzertifikat',
            \App\Enums\VaccinationImport\VaccinationImportTypeEnum::RECOVEREDVACCINATION => 'COVID-19-Impfzertifikat für Genesene',
            \App\Enums\VaccinationImport\VaccinationImportTypeEnum::BOOSTEDVACCINATION => 'COVID-19-Zertifikat - Booster',
            \App\Enums\VaccinationImport\VaccinationImportTypeEnum::BOOSTEDRECOVEREDVACCINATION => 'COVID-19-Impfzertifikat für Genesene - Booster',
            \App\Enums\VaccinationImport\VaccinationImportTypeEnum::BOOSTER => 'COVID-19-Zertifikat Booster',
        ],
    ],

    'steps' => [
        1 => 'Persönliche Daten',
        2 => 'Vereinbarung',
        3 => 'Erfassung der Daten',
        4 => 'Messungen',
        5 => 'Überblick',
    ],
];
