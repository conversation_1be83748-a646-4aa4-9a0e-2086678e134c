import defaultTheme from "tailwindcss/defaultTheme";
import forms from "@tailwindcss/forms";
const plugin = require('tailwindcss/plugin'); // Import the plugin function

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        "./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
        "./storage/framework/views/*.php",
        "./resources/views/**/*.blade.php",
        "./vendor/usernotnull/tall-toasts/config/**/*.php",
        "./vendor/usernotnull/tall-toasts/resources/views/**/*.blade.php",
        './vendor/wire-elements/pro/config/wire-elements-pro.php',
        './vendor/wire-elements/pro/**/*.blade.php',
    ],

    theme: {
        extend: {
            colors: {
                primary: {
                    DEFAULT: '#0147c2',
                    light: '#246be9',
                    dark: '#0147c2',
                    50: '#e6f0ff',
                    100: '#cce0ff',
                    200: '#99c1ff',
                    300: '#66a1ff',
                    400: '#3382ff',
                    500: '#246be9',
                    600: '#0055cc',
                    700: '#0040b3',
                    800: '#002b99',
                    900: '#0147c2',
                },
                maintext: "#D8DFEE",
                midnight: "#01031C",
                secondgray: "#62768E",
                limegreen: {
                    100: "#BCFF00",
                    150: "#76bd18",
                    200: "#6DB015",
                },
            },
            fontFamily: {
                sans: ["Nunito", ...defaultTheme.fontFamily.sans],
            },
            boxShadow: {
                primarybutton: '0px 0px 47.4px 0px rgba(1, 71, 194, 0.48)', // Equivalent to #0147C27A
            },
        },
    },

    plugins: [forms, plugin(function ({ addUtilities }) {
        addUtilities({
            '.hide-scrollbar': {
                /* Hide scrollbar for Chrome, Safari, and Opera */
                '&::-webkit-scrollbar': {
                    display: 'none',
                },
                /* Hide scrollbar for IE, Edge, and Firefox */
                '-ms-overflow-style': 'none',
                'scrollbar-width': 'none',
            },
        });
    }),],
};
