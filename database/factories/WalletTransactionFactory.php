<?php

namespace Database\Factories;

use App\Enums\WalletTransactionType;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WalletTransaction>
 */
class WalletTransactionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = WalletTransactionType::cases();
        $type = $this->faker->randomElement($types);

        $amount = match ($type) {
            WalletTransactionType::BATTLE_WIN, WalletTransactionType::DEPOSIT => $this->faker->numberBetween(100, 10000),
            default         => -$this->faker->numberBetween(100, 10000),
        };

        $balanceBefore = $this->faker->numberBetween(0, 100000);
        $balanceAfter  = $balanceBefore + $amount;

        return [
            'user_id'        => User::factory(),
            'type'           => $type,
            'amount'         => $amount,
            'balance_before' => $balanceBefore,
            'balance_after'  => $balanceAfter,
            'metadata'       => [
                'idempotency_key' => (string) Str::uuid(),
                // optional fields:
                'box_id'          => $this->faker->uuid(),
                'notes'           => $this->faker->sentence(),
            ],
        ];
    }
}
