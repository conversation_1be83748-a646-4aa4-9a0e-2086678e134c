<?php

namespace Database\Factories;

use App\Models\Box;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Promo\PromoCode>
 */
class UnboxHistoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $user = User::query()->inRandomOrder()->first();
        $box = Box::query()->inRandomOrder()->first();
        $item = $box->items()->inRandomOrder()->first();

        return [
            'user_id' => $user->id,
            'box_id' => $box->id,
            'box_name' => $box->name,
            'box_price' => $box->price,
            'item_id' => $item->id,
            'item_name' => $item->name,
            'item_price' => $item->price,
        ];
    }
}
