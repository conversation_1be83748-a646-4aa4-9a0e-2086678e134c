<?php

namespace Database\Factories;

use App\Models\PrepaidCard;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<PrepaidCard>
 */
class PrepaidCardFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => $this->faker->unique()->numerify('####-####-####-####'),
            'value' => $this->faker->randomElement([1000, 2500, 5000, 10000]),
            'redeemed_at' => null,
            'redeemed_by' => null,
        ];
    }
}
