<?php

namespace Database\Seeders;

use App\Actions\PrepaidCard\RedeemPrepaidCard;
use App\Actions\User\RecalculateBalance;
use App\Models\PrepaidCard;
use App\Models\UnboxHistory;
use App\Models\User;
use Database\Factories\UnboxHistoryFactory;
use Illuminate\Database\Seeder;

class UnboxHistorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        UnboxHistory::factory(100)->create();
    }
}
