<?php

namespace Database\Seeders;

use App\Actions\User\ProcessWalletTransaction;
use App\Enums\WalletTransactionType;
use App\Models\User;
use App\Models\WalletTransaction;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Faker\Factory as Faker;

class WalletTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();

        User::all()->each(function (User $user) use ($faker) {
            ProcessWalletTransaction::run(
                user: $user,
                type: WalletTransactionType::DEPOSIT,
                amount: 10000000, // $100000
                idempotencyKey: Str::orderedUuid(),
            );

            $count = rand(5, 15);

            for ($i = 0; $i < $count; $i++) {
                $type = $faker->randomElement(WalletTransactionType::cases());
                $amount = in_array($type, [WalletTransactionType::DEPOSIT->value, WalletTransactionType::WITHDRAWAL->value])
                    ? $faker->numberBetween(100, 5000)
                    : -$faker->numberBetween(100, 5000);

                ProcessWalletTransaction::run(
                    user: $user,
                    type: $type,
                    amount: $amount,
                    idempotencyKey: Str::orderedUuid(),
                );
            }
        });
    }
}
