<?php

namespace Database\Seeders;

use App\Actions\PrepaidCard\RedeemPrepaidCard;
use App\Actions\User\RecalculateBalance;
use App\Models\PrepaidCard;
use App\Models\User;
use Illuminate\Database\Seeder;

class PrepaidCardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        PrepaidCard::factory(10)->create();

        $client = User::query()->where('email', '<EMAIL>')->firstOrFail();

        $prepaidCards = PrepaidCard::factory(5)->create();

        foreach ($prepaidCards as $prepaidCard) {
            RedeemPrepaidCard::run($client, $prepaidCard);
        }

        RecalculateBalance::run($client);
    }
}
