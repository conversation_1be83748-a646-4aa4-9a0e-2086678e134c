<?php

namespace Database\Seeders;

use App\Models\Address;
use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::factory()->create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'is_admin' => true,
        ]);

        User::factory()
            ->has(Address::factory(), 'address')
            ->create([
                    'name' => 'User',
                    'email' => '<EMAIL>',
                ]);

        User::factory(20)->create();
    }
}
