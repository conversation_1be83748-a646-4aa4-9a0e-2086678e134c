<?php

namespace Database\Seeders;

use App\Models\Box;
use App\Models\Item;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

class BoxSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $boxFiles = File::files(storage_path('app/demo/boxes'));
        $itemFiles = File::files(storage_path('app/demo/items'));

        $boxes = Box::factory()->count(50)
            ->hasAttached(Item::factory()->count(5)
                ->create()
                ->each(function (Item $item) use ($itemFiles) {
                    $randomFile = $itemFiles[array_rand($itemFiles)];

                    $item->addMediaFromDisk("demo/items/{$randomFile->getFilename()}", 'local')
                        ->preservingOriginal()
                        ->toMediaCollection('image');
                }), [
                'probability' => 20,
            ])
            ->create([
            'is_published' => true,
        ])->each(function (Box $box) use ($boxFiles) {
            $randomFile = $boxFiles[array_rand($boxFiles)];

            $box->addMediaFromDisk("demo/boxes/{$randomFile->getFilename()}", 'local')
                ->preservingOriginal()
                ->toMediaCollection('image');

            if (rand(0, 1)) {
                $box->attachTag(tag: 'featured', type: 'highlighted');
            }
        });
    }
}
