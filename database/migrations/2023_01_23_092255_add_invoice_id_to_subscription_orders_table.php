<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddInvoiceIdToSubscriptionOrdersTable extends Migration
{
    public function up(): void
    {
        Schema::table('subscription_orders', static function (Blueprint $table) {
            $table->foreignId('invoice_id')->nullable()->constrained('invoices');
        });
    }

    public function down(): void
    {
        Schema::table('subscription_orders', static function (Blueprint $table) {
            $table->dropForeign(['invoice_id']);
            $table->dropColumn('invoice_id');
        });
    }
}
