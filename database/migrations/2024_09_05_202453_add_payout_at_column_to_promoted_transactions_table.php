<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('promoted_transactions', function (Blueprint $table) {
            $table->integer('paid_out_amount')->default(0)->after('status');
            $table->dateTime('paid_out_at')->nullable()->after('paid_out_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('promoted_transactions', function (Blueprint $table) {
            $table->dropColumn(['paid_out_amount', 'paid_out_at']);
        });
    }
};
