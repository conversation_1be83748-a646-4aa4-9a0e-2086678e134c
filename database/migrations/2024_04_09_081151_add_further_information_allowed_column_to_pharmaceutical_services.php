<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pharmaceutical_services', function (Blueprint $table) {
            $table->boolean('further_information_allowed')->default(false)->after('age_group');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pharmaceutical_services', function (Blueprint $table) {
            $table->dropColumn('further_information_allowed');
        });
    }
};
