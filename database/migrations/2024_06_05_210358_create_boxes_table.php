<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('boxes', function (Blueprint $table) {
            $table->id();
            $table->uuid()->unique()->nullable();
            $table->string('name');
            $table->text('description');
            $table->string('slug')->unique();
            $table->integer('price');
            $table->boolean('is_published')->default(false);
            $table->timestamps();
        });

        Schema::create('box_item', function (Blueprint $table) {
            $table->id();
            $table->foreignId('box_id')->constrained('boxes')->cascadeOnDelete();
            $table->foreignId('item_id')->constrained('items')->cascadeOnDelete();
            $table->float('probability');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('box_item');
        Schema::dropIfExists('boxes');
    }
};
