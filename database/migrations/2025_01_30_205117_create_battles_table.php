<?php

use App\Enums\Battle\GameType;
use App\Enums\Battle\PlayerFormation;
use App\Enums\Battle\Status;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('battles', function (Blueprint $table) {
            $table->id();
            $table->uuid()->unique()->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->boolean('is_private')->default(false);
            $table->string('game_type')->default(GameType::NORMAL->value);
            $table->string('formation')->default(PlayerFormation::ONE_VS_ONE);
            $table->bigInteger('join_price')->default(0);
            $table->dateTime('last_drop_at')->nullable();
            $table->dateTime('starts_at')->nullable();
            $table->string('status')->default(Status::OPEN);
            $table->integer('team_quantity')->default(1);
            $table->integer('player_slots')->default(1);
            $table->timestamps();
        });

        Schema::create('battle_box', function (Blueprint $table) {
            $table->id();
            $table->foreignId('battle_id')->constrained('battles')->cascadeOnDelete();
            $table->foreignId('box_id')->constrained('boxes')->cascadeOnDelete();
            $table->integer('quantity')->default(1);
            $table->integer('round')->default(1);
            $table->dateTime('opened_at')->nullable();
            $table->timestamps();
        });

        Schema::create('battle_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('battle_id')->constrained('battles')->cascadeOnDelete();
            $table->foreignId('user_id')->nullable()->constrained('users')->nullOnDelete();
            $table->integer('position')->default(1);
            $table->integer('team_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('battle_user');
        Schema::dropIfExists('battle_box');
        Schema::dropIfExists('battles');
    }
};
