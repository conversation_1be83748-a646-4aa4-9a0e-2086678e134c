<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePharmaceuticalServiceTypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pharmaceutical_service_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });

        Schema::create('pharmaceutical_service_type_pharmacy', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pharmaceutical_service_type_id');
            $table->foreignId('pharmacy_id')->constrained();
            $table->foreign('pharmaceutical_service_type_id', 'pharmaceutical_service_foreign')->references('id')->on('pharmaceutical_service_types');
            $table->timestamps();
        });

        \App\PharmaceuticalServiceType::create(['name' => 'Erweiterte Medikationsberatung bei Polymedikation']);
        \App\PharmaceuticalServiceType::create(['name' => 'Pharmazeutische Betreuung von Organtransplantierten']);
        \App\PharmaceuticalServiceType::create(['name' => 'Pharmazeutische Betreuung bei oraler Antitumortherapie']);
        \App\PharmaceuticalServiceType::create(['name' => 'Standardisierte Risikoerfassung hoher Blutdruck']);
        \App\PharmaceuticalServiceType::create(['name' => 'Standardisierte Einweisung in die korrekte Arzneimittelanwendung und Üben der Inhalationstechnik']);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pharmaceutical_service_type_pharmacy');
        Schema::dropIfExists('pharmaceutical_service_types');
    }
}
