<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('unbox_histories', function (Blueprint $table) {
            $table->bigInteger('item_price')->after('item_name')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('unbox_histories', function (Blueprint $table) {
            $table->dropColumn('item_price');
        });
    }
};
