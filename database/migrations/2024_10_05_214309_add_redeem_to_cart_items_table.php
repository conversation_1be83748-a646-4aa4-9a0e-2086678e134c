<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cart_item', function (Blueprint $table) {
            $table->integer('price')->after('item_id');
            $table->dateTime('redeemed_at')->nullable()->after('price');
            $table->string('redeemed_type')->nullable()->after('redeemed_at');
            $table->foreignId('transaction_id')->nullable()->after('redeemed_type')->constrained('transactions')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cart_item', function (Blueprint $table) {
            $table->dropForeign(['transaction_id']);
            $table->dropColumn(['price', 'redeemed_at', 'redeemed_type', 'transaction_id']);
        });
    }
};
