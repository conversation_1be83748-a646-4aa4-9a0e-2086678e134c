<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAssociationSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('association_settings', function (Blueprint $table) {
            $table->unsignedBigInteger('association_id');
            $table->boolean('can_vaccinate')->default(false);
            $table->timestamps();

            $table->primary('association_id');
            $table->foreign('association_id')
                ->references('id')
                ->on('associations')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('association_settings');
    }
}
