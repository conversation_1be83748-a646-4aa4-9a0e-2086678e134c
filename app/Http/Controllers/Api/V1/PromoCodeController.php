<?php

namespace App\Http\Controllers\Api\V1;

use App\Actions\Promo\PayoutCommission;
use App\Actions\Promo\PayoutViaCrypto;
use App\Actions\User\CreatePromoCode;
use App\Data\Api\V1\MessageData;
use App\Enums\CryptoType;
use App\Enums\MessageStatusEnum;
use App\Enums\PayoutType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\PromoCode\CreatePromoCodeRequest;
use App\Http\Requests\Api\V1\PromoCode\PayoutRequest;
use App\Http\Resources\Api\V1\MessageResource;
use App\Http\Resources\Api\V1\PromoCodeResource;
use App\Http\Resources\Api\V1\PromotedTransactionResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Money\Money;

class PromoCodeController extends Controller
{
    public function index(): JsonResource
    {
        $promoCodes = auth()->user()->promoCodes;

        return PromoCodeResource::collection($promoCodes);
    }

    public function create(CreatePromoCodeRequest $request): JsonResource
    {
        $code = $request->validated('code');

        $promoCode = CreatePromoCode::run($code, auth()->user());

        return new PromoCodeResource($promoCode);
    }

    public function promotedTransactions(): JsonResource
    {
        return PromotedTransactionResource::collection(
            auth()->user()->promotedTransactions()->orderBy('created_at', 'desc')->paginate()
        );
    }

    public function payout(PayoutRequest $request): MessageResource
    {
        $validated = $request->validated();
        $user = auth()->user();

        $availableAmount = $user->totalPayoutAbleCommisions();

        if (Money::USD(intval($validated['amount']))->greaterThan(Money::USD($availableAmount))) {
            throw new \Exception('Amount exceeds available balance.');
        }

        match ($validated['payout_type']) {
            PayoutType::BALANCE->value() => $this->payoutBalance($user, $validated['amount']),
            PayoutType::CRYPTO->value() => $this->payoutCrypto($user, $validated['amount'], $validated['crypto_type'], $validated['crypto_address']),
            default => throw new \Exception('Invalid payout type.'),
        };

        return new MessageResource(MessageData::from([
            'status' => MessageStatusEnum::SUCCESS,
            'message' => 'Payout request sent.',
        ]));
    }

    private function payoutCrypto(User $user, int $amount, string $cryptoType, string $cryptoAddress): void
    {
        PayoutViaCrypto::run($user, $amount, CryptoType::from($cryptoType), $cryptoAddress);
    }

    private function payoutBalance(User $user, int $amount): void
    {
        PayoutCommission::run($user, $amount);
    }
}
