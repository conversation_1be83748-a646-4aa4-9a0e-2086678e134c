<?php

namespace App\Http\Controllers\Api\V1;

use App\Actions\User\UpdateShippingAddress;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Profile\UpdateProfileRequest;
use App\Http\Resources\Api\V1\PublicUserResource;
use App\Http\Resources\Api\V1\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProfileController extends Controller
{
    public function update(UpdateProfileRequest $request): UserResource
    {
        $request->user()->fill($request->validated());
        $request->user()->save();

        return new UserResource(auth()->user());
    }

    public function updateAvatar(Request $request): JsonResource
    {
        $request->validate([
            'avatar' => ['required', 'file', 'image', 'max:5120'],
        ]);

        $user = auth()->user();
        $user->addMediaFromRequest('avatar')->toMediaCollection('avatar');

        return new PublicUserResource($user);
    }

    public function updateAddress(Request $request): JsonResource
    {
        $user = auth()->user();

        $updateShippingAddress = UpdateShippingAddress::make();

        $test = $request->validate($updateShippingAddress->rules());

        dd($test);

        UpdateShippingAddress::run($user, $request->validated());

        return new UserResource($user);
    }
}
