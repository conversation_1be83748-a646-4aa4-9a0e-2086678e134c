<?php

namespace App\Http\Controllers\Api\V1;

use App\Actions\PrepaidCard\RedeemPrepaidCard;
use App\Enums\MessageStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\RedeemPrepaidCardRequest;
use App\Http\Resources\Api\V1\MessageResource;
use App\Http\Resources\Api\V1\PrepaidCardResource;
use App\Models\PrepaidCard;
use App\Models\Promo\PromoCode;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use stdClass;
use Symfony\Component\HttpFoundation\Response;

class PrepaidCardController extends Controller
{
    /**
     * Redeem a prepaid card and add the value to the user's balance.
     */
    public function redeem(RedeemPrepaidCardRequest $request): JsonResponse|JsonResource
    {
        $validated = $request->validated();

        try {
            $prepaidCard = PrepaidCard::query()
                ->where('code', $validated['code'])
                ->firstOrFail();
        } catch (ModelNotFoundException $e) {
            $message = new stdClass();
            $message->status = MessageStatusEnum::ERROR;
            $message->message = 'Invalid prepaid card.';
            $message->error = 'INVALID_PREPAID_CARD';

            return (new MessageResource($message))
                ->response()
                ->setStatusCode(Response::HTTP_BAD_REQUEST);
        }

        if ($prepaidCard->isRedeemed()) {
            $message = new stdClass();
            $message->status = MessageStatusEnum::ERROR;
            $message->message = 'This prepaid card has already been redeemed.';
            $message->error = 'CARD_ALREADY_REDEEMED';

            return (new MessageResource($message))
                ->response()
                ->setStatusCode(Response::HTTP_CONFLICT);
        }

        if ($validated['promo_code']) {
            $promoCode = PromoCode::query()
                ->where('code', $validated['promo_code'])
                ->firstOrFail();
        }

        RedeemPrepaidCard::run(auth()->user(), $prepaidCard, $promoCode ?? null);

        return new PrepaidCardResource($prepaidCard);
    }
}
