<?php

namespace App\Http\Requests\Api\V1\PromoCode;

use App\Enums\CryptoType;
use App\Enums\PayoutType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PayoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => ['required', 'numeric', 'min:1'],
            'payout_type' => ['required', 'string', Rule::enum(PayoutType::class)],
            'crypto_type' => ['required_if:payout_type,'.PayoutType::CRYPTO->value(), 'string', Rule::enum(CryptoType::class)],
            'crypto_address' => ['required_if:payout_type,'.PayoutType::CRYPTO->value(), 'string'],
        ];
    }
}
