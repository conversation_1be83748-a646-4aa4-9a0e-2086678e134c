<?php

namespace App\Actions\User;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class RecalculateBalance
{
    use asAction;

    public function handle(User $user): User
    {

        DB::beginTransaction();

        $user->balance = $user->transactions()->where('status', Transaction::STATUS_COMPLETED)->sum('amount');
        $user->save();

        DB::commit();

        Return $user;
    }

}
