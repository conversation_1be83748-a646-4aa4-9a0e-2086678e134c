<?php

namespace App\Actions\Promo;

use App\Actions\User\ProcessWalletTransaction;
use App\Enums\WalletTransactionType;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class PayoutCommission
{
    use AsAction;

    public function handle(User $user, int $amount): bool
    {
        assert($amount > 0);
        assert($user->totalPayoutAbleCommisions() >= $amount);
        assert($user->tierLevel()->level() > 0);

        DB::beginTransaction();

        $payoutablePromotedTransactions = $user->promotedTransactions()
            ->whereColumn('amount', '>', 'commission')
            ->get();

        $missingAmount = $amount;
        $usedPromotedTransactions = collect();
        foreach ($payoutablePromotedTransactions as $promotedTransaction) {
            if ($missingAmount === 0) {
                break;
            }

            $payableAmount = $promotedTransaction->commission - $promotedTransaction->paid_out_amount;

            if ($payableAmount <= $missingAmount) {
                $missingAmount -= $payableAmount;
                $promotedTransaction->update([
                    'paid_out_amount' => $promotedTransaction->paid_out_amount + $payableAmount,
                    'paid_out_at' => now(),
                ]);
            } else {
                $promotedTransaction->update([
                    'paid_out_amount' => $promotedTransaction->paid_out_amount + $missingAmount,
                    'paid_out_at' => now(),
                ]);
                $missingAmount = 0;
            }
            $usedPromotedTransactions->push($promotedTransaction);
        }

        ProcessWalletTransaction::make()
            ->withoutTransaction()
            ->handle(
                user: $user,
                type: WalletTransactionType::PROMO_PAYOUT,
                amount: $amount,
                metadata: [
                    'used_promoted_transactions' => $usedPromotedTransactions->pluck('id')->toArray(),
                ],
            );

        DB::commit();

        return true;
    }
}
