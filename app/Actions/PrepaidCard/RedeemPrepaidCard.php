<?php

namespace App\Actions\PrepaidCard;

use App\Actions\Promo\CheckForStolenPromotedTransactions;
use App\Actions\User\ProcessWalletTransaction;
use App\Enums\WalletTransactionType;
use App\Models\PrepaidCard;
use App\Models\Promo\PromoCode;
use App\Models\Promo\PromotedTransaction;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class RedeemPrepaidCard
{
    use AsAction;

    public function rules(): array
    {
        return [
            'code' => ['required', 'string', 'exists:prepaid_cards,code'],
            'promo_code' => ['nullable', 'exists:promo_codes,code'],
        ];
    }

    public function handle(User $user, PrepaidCard $prepaidCard, ?PromoCode $promoCode = null): bool
    {
        if ($prepaidCard->isRedeemed()) {
            return false;
        }

        DB::beginTransaction();

        $prepaidCard->update([
            'redeemed_at' => now(),
            'redeemed_by' => $user->id,
        ]);

        $walletTransaction = ProcessWalletTransaction::make()
            ->withoutTransaction()
            ->handle(
                user: $user,
                type: WalletTransactionType::DEPOSIT,
                amount: $prepaidCard->value,
                metadata: [
                    'prepaid_card_id' => $prepaidCard->id,
                ],
            );

        if ($promoCode) {
            $promotedTransaction = PromotedTransaction::create([
                'wallet_transaction_id' => $walletTransaction->id,
                'promoter_id' => $promoCode->user_id,
                'promoted_id' => $user->id,
                'promo_code_id' => $promoCode->id,
                'code' => $promoCode->code,
                'amount' => $prepaidCard->value,
                'commission' => $prepaidCard->value * 0.05,
                'status' => PromotedTransaction::STATUS_STEALABLE,
            ]);

            CheckForStolenPromotedTransactions::run($promotedTransaction);
        }

        DB::commit();

        return true;
    }

    public function asController(ActionRequest $request): RedirectResponse
    {
        $prepaidCard = PrepaidCard::query()->where('code', $request->validated('code'))->firstOrFail();

        if ($prepaidCard->isRedeemed()) {
            return redirect()->back()->withErrors(['code' => 'This prepaid card has already been redeemed.']);
        }

        $this->handle(
            $request->user(),
            $prepaidCard
        );

        toast()->success('Prepaid card redeemed successfully!')->push();

        return redirect()->back();
    }

}
