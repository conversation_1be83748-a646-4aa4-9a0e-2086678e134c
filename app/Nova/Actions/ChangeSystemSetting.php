<?php

namespace App\Nova\Actions;

use App\Enums\StaffRoleEnum;
use App\SystemSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\KeyValue;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use RuntimeException;
use Spatie\LaravelSettings\Factories\SettingsCastFactory;

class ChangeSystemSetting extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Systemeinstellung anpassen';

    public ?SystemSetting $systemSetting = null;

    /** @phpstan-ignore-next-line */
    public function handle(ActionFields $fields): mixed
    {
        Log::info('ChangeSystemSetting::handle');
        abort_unless(auth('staff')->user()?->role === StaffRoleEnum::ADMIN, 403);

        /** @var ?\ReflectionClass $reflectionClass */
        /** @var ?\ReflectionProperty $reflectionProperty */
        [$reflectionClass, $reflectionProperty] = $this->getSettingClassAndProperty();

        if (! $reflectionClass || ! $reflectionProperty) {
            throw new RuntimeException('Setting class cannot be resolved.');
        }

        $payload = $fields->get('payload');
        /** @var ?\ReflectionNamedType $reflectionType */
        $reflectionType = $reflectionProperty->getType();
        if (! $reflectionType?->isBuiltin()) {
            $settingsCast = SettingsCastFactory::resolve($reflectionProperty, []);
            if (! $settingsCast) {
                throw new RuntimeException('No caster found for type `'.$reflectionProperty->getType().'`.');
            }
            $payload = $settingsCast->get($payload);
        }

        $settingClass = app($reflectionClass->getName());
        $settingClass->{$reflectionProperty->getName()} = $payload;
        $settingClass->save();

        return Action::message('Wert erfolgreich aktualisiert.');
    }

    public function getSystemSetting(): SystemSetting
    {
        if (! $this->systemSetting) {
            $request = request();
            /** @var SystemSetting $systemSetting */
            /** @phpstan-ignore-next-line */
            $systemSetting = SystemSetting::findOrFail($request->resourceId ?? (int) ($request->resources[0] ?? throw new RuntimeException('No resourceId found.')));
            $this->systemSetting = $systemSetting;
        }

        return $this->systemSetting;
    }

    /**
     * @return array<Field>
     */
    public function fields(NovaRequest $request): array
    {
        $payloadField = $this->getPayloadField();

        return [
            Text::make('Gruppe', 'group')
                ->readonly()
                ->default($this->getSystemSetting()->group),
            Text::make('Variable', 'name')
                ->readonly()
                ->default($this->getSystemSetting()->name),
            $payloadField,
        ];
    }

    private function getPayloadField(): Field
    {
        /** @var string $payloadType */
        /** @var bool $nullable */
        [$payloadType, $nullable] = $this->getPayloadType();

        $default = json_decode($this->getSystemSetting()->payload);
        $rules = [$nullable ? 'nullable' : 'required'];

        switch ($payloadType) {
            case 'int':
                return Number::make('Wert', 'payload')
                    ->default($default)
                    ->rules(array_merge($rules, ['integer']));
            case 'float':
                return Number::make('Wert', 'payload')
                    ->step('any')
                    ->default($default)
                    ->rules(array_merge($rules, ['decimal:0,50']));
            case 'string':
                return Text::make('Wert', 'payload')
                    ->default($default)
                    ->rules(array_merge($rules, ['string']));
            case 'bool':
                return Boolean::make('Wert', 'payload')
                    ->default($default)
                    ->rules(array_merge($rules, ['boolean']));
            case 'array':
                return KeyValue::make('Wert', 'payload')
                    ->default($default)
                    ->rules(array_merge($rules, ['json']));
            default:
                if ($payloadType) {
                    /** @var array<class-string, callable> $novaFieldsByType */
                    $novaFieldsByType = config('settings.nova_fields');

                    foreach ($novaFieldsByType as $parentClass => $getNovaFieldClass) {
                        $getNovaField = new $getNovaFieldClass;
                        if (is_subclass_of($payloadType, $parentClass) && is_callable($getNovaField)) {
                            $novaField = $getNovaField($default);

                            return $novaField->nullable($nullable)
                                ->rules(array_merge($rules, $novaField->rules));
                        }
                    }
                }

                throw new RuntimeException(
                    'Nova field mapping is missing. No Nova field is configured for the data type `'.$payloadType.'`. Check the configuration `settings.nova_fields`.'
                );
        }
    }

    /**
     * @return array<int, string|bool>|null[]
     *
     * @throws \ReflectionException
     */
    private function getPayloadType(): array
    {
        /** @var ?\ReflectionClass $reflectionClass */
        /** @var ?\ReflectionProperty $reflectionProperty */
        [$reflectionClass, $reflectionProperty] = $this->getSettingClassAndProperty();

        if ($reflectionClass && $reflectionProperty) {
            $type = $reflectionProperty->getType();

            if (! $type instanceof \ReflectionNamedType) {
                throw new RuntimeException(
                    'The data type `'.$type.'` must be an instance of ReflectionNamedType. ReflectionUnionType and ReflectionIntersectionType are not allowed.'
                );
            }

            return [$type->getName(), $type->allowsNull()];
        }

        return [null, null];
    }

    /**
     * @return array<int, \ReflectionClass|\ReflectionProperty>|null[]
     *
     * @throws \ReflectionException
     */
    private function getSettingClassAndProperty(): array
    {
        $name = $this->getSystemSetting()->name;
        $group = $this->getSystemSetting()->group;

        /** @var array<class-string> $settingClasses */
        $settingClasses = config('settings.settings');
        foreach ($settingClasses as $settingClass) {
            $settingObject = app($settingClass);

            if (
                ! is_object($settingObject)
                || ! method_exists($settingObject, 'group')
                || $settingObject->group() !== $group
            ) {
                continue;
            }

            $reflectionClass = new \ReflectionClass($settingClass);

            if (! $reflectionClass->hasProperty($name)) {
                continue;
            }

            $reflectionProperty = $reflectionClass->getProperty($name);

            return [$reflectionClass, $reflectionProperty];
        }

        return [null, null];
    }
}
