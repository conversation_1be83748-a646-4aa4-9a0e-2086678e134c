name: Publish Docker image

on:
  push:
    branches: ['main']

jobs:
  push_to_registry:
    name: Push Docker image to Docker Hub
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          registry: registry.digitalocean.com/dropkingdom
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: registry.digitalocean.com/dropkingdom/webapp

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          build-args: |
            COMPOSER_WE_USER=${{ secrets.COMPOSER_WE_USER }}
            COMPOSER_WE_PASS=${{ secrets.COMPOSER_WE_PASS }}
          file: ./Dockerfile
          target: prod
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
