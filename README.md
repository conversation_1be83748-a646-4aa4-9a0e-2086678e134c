# DropKingdom

## Installation

### System requirements for development

* Docker
* Docker Compose

### Clone the repository

```bash
<NAME_EMAIL>:eack/DropKingdom.git
```

### Copy and configure .env file

```bash
cp .env.example .env
```

### Install composer dependencies

```bash
docker run --rm \
    -u "$(id -u):$(id -g)" \
    -v "$(pwd):/var/www/html" \
    -w /var/www/html \
    laravelsail/php83-composer:latest \
    composer install
```

### Start the docker containers

```bash
docker compose up -d
```

### Generate a secret

```bash
docker compose exec app php artisan key:generate
```

### Migrate and seed the database

```bash
docker compose exec app php artisan migrate:fresh --seed
```

### Install npm packages

```bash
docker compose exec app npm install
```

### Start frontend development server

```bash
docker compose exec app npm run dev
```

### Access the application

The application is now accessible at [http://localhost:8080](http://localhost:8080).
