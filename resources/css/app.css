@import "../../vendor/wire-elements/pro/resources/css/tailwind/overlay-component.css";

@import 'fonts';

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
    letter-spacing: .5px;
}

.wep-modal-content {
    @apply bg-[#1B1E3A] rounded-3xl;
}

.title {
    background-image: linear-gradient(to bottom,var(--tw-gradient-stops));
    --tw-gradient-from: #ABADBE var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(171 173 190 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
    --tw-gradient-to: #ffffff var(--tw-gradient-to-position);
    -webkit-background-clip: text;
    background-clip: text;
    font-weight: 800;
    -webkit-text-fill-color: transparent
}

.filepond--credits {
    display: none
}

.filepond--panel-root {
    @apply !bg-[#0E1527] !cursor-pointer;
    border: 2px solid #2c3340;
}

.filepond--panel-root {
    @apply border border-dashed border-[#333D4F] rounded-2xl;
}

.filepond--drop-label {
    color: #fff !important;
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-LightItalic.eot") format("embedded-opentype"),
         url("/resources/fonts/Gotham-LightItalic.woff2") format("woff2"),
         url("/resources/fonts/Gotham-LightItalic.woff") format("woff"),
         url("/resources/fonts/Gotham-LightItalic.ttf") format("truetype");
    font-weight: 300;
    font-style: italic
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-BoldItalic.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-BoldItalic.woff2") format("woff2"),
    url("/resources/fonts/Gotham-BoldItalic.woff") format("woff"),
    url("/resources/fonts/Gotham-BoldItalic.ttf") format("truetype");
    font-weight: 700;
    font-style: italic
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-XLight.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-XLight.woff2") format("woff2"),
    url("/resources/fonts/Gotham-XLight.woff") format("woff"),
    url("/resources/fonts/Gotham-XLight.ttf") format("truetype");
    font-weight: 200;
    font-style: normal
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-Thin.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-Thin.woff2") format("woff2"),
    url("/resources/fonts/Gotham-Thin.woff") format("woff"),
    url("/resources/fonts/Gotham-Thin.ttf") format("truetype");
    font-weight: 100;
    font-style: normal
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-Book.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-Book.woff2") format("woff2"),
    url("/resources/fonts/Gotham-Book.woff") format("woff"),
    url("/resources/fonts/Gotham-Book.ttf") format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-ThinItalic.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-ThinItalic.woff2") format("woff2"),
    url("/resources/fonts/Gotham-ThinItalic.woff") format("woff"),
    url("/resources/fonts/Gotham-ThinItalic.ttf") format("truetype");
    font-weight: 100;
    font-style: italic
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-MediumItalic.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-MediumItalic.woff2") format("woff2"),
    url("/resources/fonts/Gotham-MediumItalic.woff") format("woff"),
    url("/resources/fonts/Gotham-MediumItalic.ttf") format("truetype");
    font-weight: 500;
    font-style: italic
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-UltraItalic.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-UltraItalic.woff2") format("woff2"),
    url("/resources/fonts/Gotham-UltraItalic.woff") format("woff"),
    url("/resources/fonts/Gotham-UltraItalic.ttf") format("truetype");
    font-weight: 400;
    font-style: italic
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-BookItalic.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-BookItalic.woff2") format("woff2"),
    url("/resources/fonts/Gotham-BookItalic.woff") format("woff"),
    url("/resources/fonts/Gotham-BookItalic.ttf") format("truetype");
    font-weight: 400;
    font-style: italic
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-BlackItalic.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-BlackItalic.woff2") format("woff2"),
    url("/resources/fonts/Gotham-BlackItalic.woff") format("woff"),
    url("/resources/fonts/Gotham-BlackItalic.ttf") format("truetype");
    font-weight: 900;
    font-style: italic
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-Light.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-Light.woff2") format("woff2"),
    url("/resources/fonts/Gotham-Light.woff") format("woff"),
    url("/resources/fonts/Gotham-Light.ttf") format("truetype");
    font-weight: 300;
    font-style: normal
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-Bold.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-Bold.woff2") format("woff2"),
    url("/resources/fonts/Gotham-Bold.woff") format("woff"),
    url("/resources/fonts/Gotham-Bold.ttf") format("truetype");
    font-weight: 700;
    font-style: normal
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-Medium.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-Medium.woff2") format("woff2"),
    url("/resources/fonts/Gotham-Medium.woff") format("woff"),
    url("/resources/fonts/Gotham-Medium.ttf") format("truetype");
    font-weight: 500;
    font-style: normal
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-Ultra.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-Ultra.woff2") format("woff2"),
    url("/resources/fonts/Gotham-Ultra.woff") format("woff"),
    url("/resources/fonts/Gotham-Ultra.ttf") format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-XLightItalic.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-XLightItalic.woff2") format("woff2"),
    url("/resources/fonts/Gotham-XLightItalic.woff") format("woff"),
    url("/resources/fonts/Gotham-XLightItalic.ttf") format("truetype");
    font-weight: 200;
    font-style: italic
}

@font-face {
    font-family: Gotham;
    src: url("/resources/fonts/Gotham-Black.eot") format("embedded-opentype"),
    url("/resources/fonts/Gotham-Black.woff2") format("woff2"),
    url("/resources/fonts/Gotham-Black.woff") format("woff"),
    url("/resources/fonts/Gotham-Black.ttf") format("truetype");
    font-weight: 900;
    font-style: normal
}

.hexagon {
    clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
}
