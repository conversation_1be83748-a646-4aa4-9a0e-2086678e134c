<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Home') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <header>
                        <h2 class="text-lg font-medium text-gray-900">ALL BOXES</h2>
                        <div>
                            <span class="isolate inline-flex rounded-md shadow-sm">
                                <a href="{{ route('boxes.index', ['category' => 'featured']) }}" wire:navigate>
                                  <button type="button" @class([
                                      "bg-indigo-600 text-white" => $category === 'featured',
                                      "bg-white text-gray-900" => $category !== 'featured',
                                      "relative inline-flex items-center rounded-l-md px-3 py-2 text-sm font-semibold  ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10",
                                  ])>FEATURED</button>
                                </a>
                                <a href="{{ route('boxes.index', ['category' => 'hot']) }}" wire:navigate>
                                  <button type="button"
                                          @class([
                                      "bg-indigo-600 text-white" => $category === 'hot',
                                      "bg-white text-gray-900" => $category !== 'hot',
                                          "relative -ml-px inline-flex items-center px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10"
                                          ])>HOT</button>
                                    </a>

                            <select x-data="{ link : '{{ request()->fullUrl() }}' }" x-model="link" x-init="$watch('link', value => window.location = link)" class="-ml-px block w-full rounded-l-none rounded-r-md border-0 bg-white py-1.5 pl-3 pr-9 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                <option value="{{ route('boxes.index', ['category' => '']) }}">Recommended</option>
                                <option value="{{ route('boxes.index', ['category' => $category ?? '', 'sort' => 'price']) }}" @selected(request()->get('sort') == 'price')>Price (Low to High)</option>
                                <option value="{{ route('boxes.index', ['category' => $category ?? '', 'sort' => '-price']) }}" @selected(request()->get('sort') == '-price')>Price (High to Low)</option>
                            </select>
                                </span>
                        </div>
                    </header>
                    <x-boxes.boxes-body :boxes="$boxes"/>
                    {{ $boxes->links() }}
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
