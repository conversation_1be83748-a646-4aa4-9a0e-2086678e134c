<div {{ $attributes->merge(['class' => 'relative h-56 flex items-center justify-center shrink-0 overflow-hidden rounded-[8px] bg-[linear-gradient(180deg,#0B0D23_0%,#2B2741_126.79%)] w-[194px]']) }}>
    <div class="absolute right-3 top-3 z-[2] flex flex-col items-end">
        <span class="text-[8px] font-bold uppercase text-white/50">Chance</span>
        <span class="text-[10px] font-normal text-[#578FFC]">{{ $item->pivot->probability }} %</span>
    </div>

    <div class="absolute bottom-3 z-[3] flex w-full flex-col items-center">
        <div class="relative w-36 h-28 mb-2 p-4 flex items-center justify-center overflow-hidden">
            <img class="w-full h-full object-contain" alt="{{ $item->name }}" src="{{ $item->getFirstMedia('image')->getTemporaryUrl(\Carbon\Carbon::now()->addMinutes(5), 'thumb') }}">
        </div>
        <h3 class="text-xs font-normal text-white">{{ $item->name }}</h3>
        <p class="mt-[2px] text-xs font-bold text-white/50">{{ $item->tagsWithType('itemCategories')->first()?->name }}</p>
        <span class="mt-[8px] text-[13px] font-normal text-[#89E9AF]">{{ money($item->price) }}</span>
    </div>

    <img alt="bg" class="absolute inset-0 z-[1] h-full w-full object-cover" src="{{ Vite::asset('resources/images/good/bg/blue.png') }}">
</div>
