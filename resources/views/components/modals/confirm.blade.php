@props([
    'disabled' => false,
])
<div class="flex flex-col items-start rounded-[30px] px-[37px] py-[26px] [background:linear-gradient(338deg,#11172A_-1.69%,#11172A_85.79%,#3C4E90_140.75%)]">
    <form wire:submit="submit">
        <div class="flex justify-between">
            @if ($title)
                <h2 class="text-base font-bold text-white">
                    {{ $title }}
                </h2>
            @endif
        </div>

        {{ $slot }}

        <div class="flex w-full gap-2.5 mt-[10px]">
            <button wire:click="close()" type="button" class="ml-auto flex h-[50px] items-center justify-center gap-3 rounded-xl px-5 py-[15px] text-xs font-bold uppercase text-[#D7D9E7] [background:#454666]">Cancel</button>
            <button type="submit" @if($disabled) disabled @endif @class([
                "flex h-[50px] items-center justify-center gap-3 rounded-xl px-5 py-[15px] text-xs font-bold uppercase text-[#D7D9E7]",
                "[background:#4046C1]" => !$disabled,
                "[background:#454666]" => $disabled,
            ])>Submit</button>
        </div>
    </form>
</div>
