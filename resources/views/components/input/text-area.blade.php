@props([
    'icon' => null,
    'error' => null,
    'width' => 'w-full'
])

<div>
    <div class="relative flex items-center text-gray-400 focus-within:text-gray-600">
        <textarea
            {{ $attributes->class([
                'text-white bg-[#252843] border-transparent focus:border-transparent pr-3 py-2 font-semibold placeholder-gray-500 rounded-lg border-[#2F324E] ring-1 ring-[#2F324E] focus:ring-gray-500 focus:ring-1',
                'pl-10' => !is_null($icon),
                'form-input focus:ring-red-600 ring-red-600 text-red-900 placeholder-red-600' => $error,
                $width,
            ]) }}
            {{ $attributes }}
            rows="4"
        ></textarea>
    </div>
    @if ($error)
        <p class="{{ $width }} mt-2 text-sm text-red-600">{{ $error }}</p>
    @endif
</div>
