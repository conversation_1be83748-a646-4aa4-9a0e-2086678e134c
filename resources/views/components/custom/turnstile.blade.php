<div
    id="turnstile-container"
    class="cf-turnstile"
    data-sitekey="{{ config('services.turnstile.key') }}"
    @if ($attributes->has('wire:model'))
        wire:ignore
        x-init="onloadTurnstileCallback"
    x-data="{ onloadTurnstileCallback() {
        turnstile.render('#turnstile-container', {
            sitekey: '{{ config('services.turnstile.key') }}',
            callback: function (token) {
                @this.set('{{ $attributes->get('wire:model') }}', token);
            },
        });
    } }"
    {{ $attributes->filter(fn($value, $key) => ! in_array($key, ['data-callback', 'wire:model'])) }}
@else
    {{ $attributes->whereStartsWith('data-') }}
    @endif
></div>

<script>
    window.onloadTurnstileCallback = function () {
        console.log('load turnstile')
        turnstile.render("#turnstile-container", {
            sitekey: "{{ config('services.turnstile.key') }}",
            callback: function (token) {
                console.log(`Challenge Success ${token}`);
            },
        });
    };
</script>
