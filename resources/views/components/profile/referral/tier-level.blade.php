@props([
    'active' => false,
])

<article
    class="border-dashed border border-[#333D4F] flex w-full flex-col items-start gap-6 rounded-3xl p-5 bg-[linear-gradient(180deg,#060D21_0%,#131A2E_100%)]">
    <div class="flex gap-2.5">
        <div
            class="flex h-12 w-12 flex-col items-center justify-center gap-2.5 rounded-2xl px-5 py-1.5 text-sm font-bold text-[#96ACE5] bg-[#192034]">
            {{ $slot }}
        </div>

        <div class="flex flex-col justify-center gap-2.5">
            <span class="text-[13px] font-bold text-white">
                <span>Level</span>
            </span>
        </div>
    </div>

    <ul class="space-y-3">
        {{ $conditions }}
    </ul>
</article>
