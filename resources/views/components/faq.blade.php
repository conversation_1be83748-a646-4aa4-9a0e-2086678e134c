<div class="flex w-full flex-col gap-3 overflow-hidden rounded-[16px] bg-[#5F6C87]/20 p-6" x-data="{ isExpanded: false }">
    <button :aria-expanded="isExpanded ? 'true' : 'false'" @click="isExpanded = !isExpanded" aria-controls="0" class="flex h-[22px] w-full items-center justify-between text-lg font-bold text-white" id="controls0" type="button" aria-expanded="false">
        {{ $slot }}
        <svg :class="isExpanded ? 'rotate-180' : ''" aria-hidden="true" class="size-5 shrink-0 transition" fill="none" stroke-width="2" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M19.5 8.25l-7.5 7.5-7.5-7.5" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
    </button>

    <div aria-labelledby="controls0" id="0" role="region" x-collapse="" x-show="isExpanded" style="display: none;">
        <p class="text-sm font-bold text-[#BDC5D4]">{{ $answer }}</p>
    </div>
</div>
