@props([
    'active' => false,
])

<a {{ $attributes->merge(['class' => 'mt-10 flex flex-col items-center gap-1.5 hover:text-white', 'href' => '#']) }}>
    @if (isset($icon))
        <div @class([
            'p-3 flex items-center justify-center',
            'bg-[linear-gradient(180deg,#7187FA_0%,#3749A6_100%)] text-white rounded-md' => $active
        ])>
            <div class="h-[26px] w-[26px]">
                {{ $icon }}
            </div>
        </div>
    @endif
    <span @class([
            'text-xs font-semibold text-nowrap',
            'text-white' => $active
        ])>{{ $slot }}</span>
</a>
