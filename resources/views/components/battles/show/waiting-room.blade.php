<div class="grid grid-cols-1 md:grid-cols-{{ $battle->team_quantity }} lg:grid-cols-{{ $battle->team_quantity }} lg:space-x-4 relative">
    @php $position = 1; $iPosition = 1; @endphp
    @foreach(range(1, $battle->team_quantity) as $teamId)
        <div class="my-2">
            <div class="h-fit overflow-x-auto overflow-y-hidden hide-scrollbar">

                <div class="flex space-x-4 relative h-fit">
                    @foreach(range(1, $battle->formation->maxPlayersPerTeam()) as $playerId)
                        @php $player = $players->where('position', $position)->first()->user; @endphp
                        <div class="flex-1 w-full flex-wrap md:flex-grow ">
                            <div class="bg-[#060D21] w-full border-[1px] border-[#111C30] rounded-3xl p-4 h-96 flex items-center justify-center">
                                @if (!is_null($player))
                                    <div class="relative">
                                        @php $lastDrop = $lastDrops->get($player->id) @endphp
                                        @if ($lastDrop)
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <img src="{{ $lastDrop->item->getFirstMedia('image')->getTemporaryUrl(\Carbon\Carbon::now()->addMinutes(5), 'thumb')  }}" alt="item" class="p-14 w-full"/>
                                        </div>
                                        @endif
                                        <img src="{{ Vite::asset('resources/images/battle/hexagon.png') }}" alt="hexagon" class="w-[245px]">
                                    </div>
                                @else
                                    <div class="w-full text-center">
                                        <x-buttons.primary-button wire:click="join({{ $position }})">Join for {{ money($battle->join_price) }}</x-buttons.primary-button>
                                    </div>
                                @endif
                            </div>
                            <div class="m-4 flex items-center space-x-6">
                                <div class="w-11 relative flex aspect-square items-center justify-center rounded-full border-[2px] border-[#1E61ED]">
                                    @if (!is_null($player))
                                        <img alt="avatar" class="rounded-full" src="{{ $player?->avatar ?? Vite::asset('resources/images/avatar/avatar.png') }}">
                                    @endif
                                    <img alt="rays" class="absolute opacity-40 scale-[2.5]" src="{{ Vite::asset('resources/images/avatar/rays.svg') }}">
                                    <img alt="rays" class="absolute opacity-30 scale-[2.5]" src="{{ Vite::asset('resources/images/avatar/circle.svg') }}">
                                </div>

                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-white">{{ $player?->name ?? 'Waiting for player' }}</span>
                                    @if (!is_null($player))
                                        <strong class="text-sm font-semibold text-[#246BE9]">
                                            {{ money($playerValues->filter(fn ($playerValue) => $playerValue->user_id === $player?->id)->first()?->total) }}
                                        </strong>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @php $position++; @endphp
                    @endforeach
                    @if (!$loop->last)
                        <div class="absolute -right-[44px] z-10 top-1/3 flex items-center justify-center text-white">
                            <img src="{{ Vite::asset('resources/images/battle/battle-hexagon.png') }}" alt="battle" class="w-[35px] hidden md:w-[70px]">
                        </div>
                    @endif
                </div>
                <div class="bg-[#060D21] border-[1px] border-[#111C30] p-2 rounded-lg col-span-2 w-full text-sm font-medium text-[#62768E] text-center">
                    @if ($battle->isOpen())
                        Waiting For Players
                    @else
                        Team Value: <span class="text-white">{{ money($teamValues->filter(fn ($teamValue) => $teamValue->team_id === $teamId)->first()?->total) }}</span>
                    @endif
                </div>
                <div class="flex space-x-4 relative">
                    @foreach(range(1, $battle->formation->maxPlayersPerTeam()) as $playerId)
                        @php $player = $players->where('position', $iPosition)->first()->user; @endphp
                        <div class="mt-4 grid grid-cols-3 gap-4 justify-items-stretch">
                        @foreach($this->drops->filter(fn ($drop) => $drop->user_id === $player->id) as $drop)
                            @php $item = $drop->box->items->firstWhere('id', $drop->item->id); @endphp
                                @if ($item)
                            <x-items.single :item="$item" class="w-44" />
                                @endif
                        @endforeach
                        </div>
                        @php $iPosition++; @endphp
                    @endforeach
                </div>
            </div>
        </div>
    @endforeach

    @php /*
    @for ($x = 0; $x < 4; $x++)
        @endfor


    <div class="bg-[#060D21] border-[1px] border-[#111C30] p-2 rounded-lg col-span-2 w-full text-sm font-medium text-[#62768E] text-center">
        Waiting For Players
    </div>
    */ @endphp
</div>
