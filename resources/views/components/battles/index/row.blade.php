<div class="mb-6">
    <div class="flex relative max-w-[95vw] flex-col  xl:flex-row md:px-5 gap-8 lg:gap-2 h-fit xl:h-40 rounded-3xl border-2 border-solid border-[#0B172D] bg-[#02091D] xl:bg-[#060D21] lg:p-[6px]">
        <div class="basis-32 items-center justify-center flex flex-col">
            <x-battles.index.rounds>{{ $battle->rounds() }}</x-battles.index.rounds>

            <div class="text-center text-xs font-bold text-[#4A4C5C]">
                ROUNDS
            </div>
        </div>

        <div class="w-full  flex flex-wrap lg:flex-nowrap  h-full items-center justify-center gap-[20px] bg-[#02091D] lg:px-[15px]">
            @foreach($battle->boxes->slice(0, 5) as $box)
                <img alt="box" class="h-[80px] w-auto object-contain" src="{{ $box->getFirstMedia('image')->getTemporaryUrl(\Carbon\Carbon::now()->addMinutes(5), 'thumb') }}">
            @endforeach
        </div>

        <div class="flex gap-8 justify-center items-center">

            <div class="basis-32 flex h-full w-[114px] shrink-0 items-center justify-center bg-[#02091D]">
                <span class="text-center text-sm font-bold text-[#B2D64E]">{{ money($battle->join_price) }}</span>
            </div>

            <div class="basis-44 flex h-full w-[154px] lg:shrink-0 flex-col items-center justify-center gap-[15px] bg-[#02091D]">
                <span class="text-xs font-bold text-[#62768E]">{{ $battle->formation }}</span>

                <div class="flex gap-2">
                    @foreach($battle->users as $user)
                        <img alt="user" class="h-6 w-6 rounded-full" src="{{ $user->avatar ?? Vite::asset('resources/images/avatar/avatar.png') }}">
                    @endforeach
                    @if ($battle->remainingPlayerSlots() > 0)
                        @for ($i = 0; $i < $battle->remainingPlayerSlots(); $i++)
                            <div class="h-6 w-6 rounded-full bg-[#12192D]"></div>
                        @endfor
                    @endif
                </div>
            </div>

        </div>

        <div class="basis-44 flex w-full xl:w-[177px] shrink-0 items-center justify-center px-[20px]">
            <a href="{{ route('battles.show', ['battle' => $battle]) }}">
                @if ($battle->isOpen())
                    <x-buttons.primary-button>
                        <x-lucide-swords class="w-5 h-5 mr-2"/>
                        Join
                    </x-buttons.primary-button>
                @else
                    <x-buttons.secondary-button>
                        <x-lucide-eye class="w-5 h-5 mr-2"/>
                        Watch
                    </x-buttons.secondary-button>
                @endif
            </a>
        </div>
    </div>
</div>
