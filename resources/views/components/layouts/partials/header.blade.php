<header class="flex p-4 justify-between flex-wrap h-20">
    <div class="flex gap-6 flex-wrap">
        <a href="{{ route('home') }}" wire:navigate>

            <svg class="w-32 h-12" viewBox="0 0 1144 312" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M987.3 311.6C1073.35 311.6 1143.1 241.846 1143.1 155.8C1143.1 69.754 1073.35 0 987.3 0C901.254 0 831.5 69.754 831.5 155.8C831.5 241.846 901.254 311.6 987.3 311.6Z" fill="#1081F6"/>
                <path d="M162 75.2H134.6L81.5 164.7L27.6 75.2H0V219.2H31.3V134.6L73.5 203.9H88.5L130.9 132.7L131.1 219.1H162.4L162 75.2Z" fill="white"/>
                <path d="M265.1 108.5L233.8 183.6L202.7 108.5H169.6L213.8 211.4C222.4 205 227.8 195.6 227.8 195.6C226.1 216.7 212.2 229.6 210.9 230.8C210.8 230.9 210.8 230.9 210.8 230.9C207.7 233.7 204 234.9 199.1 234.9C192.7 234.9 185.9 232.2 181 227.9L169.3 250.7C176.5 257.1 188.8 260.8 200.2 260.8C220.2 260.8 235.4 252.8 246.1 226L296.1 108.5H265.1Z" fill="white"/>
                <path d="M368.6 75.2H303.2V219.2H368.6C415.7 219.2 448 190.8 448 147.2C448 103.5 415.7 75.2 368.6 75.2ZM367 191.8H336.6V102.5H367C395.6 102.5 414.3 119.6 414.3 147.1C414.3 174.6 395.6 191.8 367 191.8Z" fill="#1081F6"/>
                <path d="M493.3 123.1V108.5H462.6V219.2H494.7V166.9C494.7 145.7 506.4 136 524.1 136C526.6 136 528.6 136.2 531.3 136.4V106.8C514.3 106.8 501.1 112.4 493.3 123.1Z" fill="#1081F6"/>
                <path d="M593.9 106.8C558.7 106.8 533 130.5 533 163.8C533 197.1 558.7 220.8 593.9 220.8C629.1 220.8 654.6 197.1 654.6 163.8C654.6 130.5 629.1 106.8 593.9 106.8ZM593.9 194.5C577.9 194.5 565.5 183 565.5 163.8C565.5 144.7 577.8 133.1 593.9 133.1C610 133.1 622.1 144.6 622.1 163.8C622.1 183 609.9 194.5 593.9 194.5Z" fill="#1081F6"/>
                <path d="M733.2 106.8C718.4 106.8 706.5 111.5 698.4 121.2V108.4H667.7V259H699.8V218.9C691.3 207.7 690 193.9 690 193.9C698 207.5 707.5 214.3 714.9 217.6C714.9 217.6 714.9 217.6 715 217.6C720.4 219.7 726.5 220.7 733.2 220.7C764.3 220.7 788.3 198.5 788.3 163.7C788.3 129.1 764.2 106.8 733.2 106.8ZM727.6 194.5C711.5 194.5 699.4 183 699.4 163.8C699.4 144.7 711.5 133.1 727.6 133.1C743.6 133.1 755.8 144.6 755.8 163.8C755.8 183 743.7 194.5 727.6 194.5Z" fill="#1081F6"/>
                <path d="M81.2998 50.8999C81.2998 65.7999 69.1998 77.8999 54.2998 77.8999C69.1998 77.8999 81.2998 89.9999 81.2998 104.9C81.2998 89.9999 93.3998 77.8999 108.3 77.8999C93.3998 77.8999 81.2998 65.7999 81.2998 50.8999Z" fill="#1081F6"/>
                <path d="M893.6 200.5C922.2 176.3 952.1 153.9 952.1 130.8C952.1 119.6 947.1 111.9 935.1 111.9C923.5 111.9 917.1 120.2 916.9 133.5H893.4C894.3 104.6 912.8 91.3 935.5 91.3C962.7 91.3 976.3 107.4 976.3 129.4C976.3 158.6 948.1 181.5 928 198.5H979.1V218.6H893.6V200.5Z" fill="#131416"/>
                <path d="M1007.8 174.6L1043.3 121.6V161.6H1066V95.7002H1037.8L981.2 176.7V195.7H1041.8V220.3H1066V195.7H1081.2V174.6H1007.8Z" fill="#131416"/>
            </svg>

        </a>

        <div class="w-px h-13 bg-[#343E59]"></div>

        <div class="hidden lg:inline content-center font-light">
            <a href="/" class="flex gap-2" wire:navigate>
                <svg fill="none" height="18" viewBox="0 0 20 22" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path clip-rule="evenodd"
                          d="M2.29909 8.72889C2.55837 8.64126 2.83614 8.59375 3.125 8.59375C4.54884 8.59375 5.70312 9.74803 5.70312 11.1719V14.2656C5.70312 15.6895 4.54884 16.8438 3.125 16.8438C1.70116 16.8438 0.546875 15.6895 0.546875 14.2656V9.45312C0.546875 4.23231 4.77918 0 10 0C15.2208 0 19.4531 4.23231 19.4531 9.45312V15.9844C19.4531 18.3575 17.5294 20.2812 15.1562 20.2812H12.4314C12.0775 21.2826 11.1225 22 10 22C8.57614 22 7.42188 20.8457 7.42188 19.4219C7.42188 17.998 8.57614 16.8438 10 16.8438C11.1225 16.8438 12.0775 17.5612 12.4314 18.5625H15.1562C16.3197 18.5625 17.3031 17.7919 17.6239 16.7333C17.3869 16.8051 17.1355 16.8438 16.875 16.8438C15.4512 16.8438 14.2969 15.6895 14.2969 14.2656V11.1719C14.2969 9.74803 15.4512 8.59375 16.875 8.59375C17.1639 8.59375 17.4416 8.64126 17.7009 8.72889C17.3358 4.7969 14.0274 1.71875 10 1.71875C5.97264 1.71875 2.66423 4.7969 2.29909 8.72889ZM17.7344 11.1719C17.7344 10.6973 17.3496 10.3125 16.875 10.3125C16.4004 10.3125 16.0156 10.6973 16.0156 11.1719V14.2656C16.0156 14.7402 16.4004 15.125 16.875 15.125C17.3496 15.125 17.7344 14.7402 17.7344 14.2656V11.1719ZM2.26562 11.1719C2.26562 10.6973 2.6504 10.3125 3.125 10.3125C3.5996 10.3125 3.98438 10.6973 3.98438 11.1719V14.2656C3.98438 14.7402 3.5996 15.125 3.125 15.125C2.6504 15.125 2.26562 14.7402 2.26562 14.2656V11.1719ZM10 18.5625C9.52538 18.5625 9.14062 18.9473 9.14062 19.4219C9.14062 19.8965 9.52538 20.2812 10 20.2812C10.4746 20.2812 10.8594 19.8965 10.8594 19.4219C10.8594 18.9473 10.4746 18.5625 10 18.5625Z"
                          fill-rule="evenodd" fill="#62768E"></path>
                </svg>

                <span class="text-secondgray text-xs">Support</span>
            </a>
        </div>

        <div class="hidden lg:inline content-center font-light">
            <a href="{{ route('affiliate') }}" class="flex gap-2" wire:navigate>
                <x-svg-icon icon="affiliate" class="w-5 h-5" />

                <span class="text-white text-xs">Partnerships &amp; Affiliate Program</span>
            </a>
        </div>
    </div>

    <div class="hidden md:inline">
    @if (auth()->check())
        <livewire:header.profile />
    @else
        <div class="flex gap-4 items-center font-light">
            <x-buttons.primary-button onclick="Livewire.dispatch('modal.open', { component: 'auth.authentication-modal' })" class="gap-2 text-nowrap">
                <x-svg-icon icon="login" class="w-4 h-4" />

                <span>SIGN UP</span>
            </x-buttons.primary-button>
            <x-buttons.secondary-button onclick="Livewire.dispatch('modal.open', { component: 'auth.authentication-modal', arguments: { isLogin: true } })" class="gap-2">
                <x-svg-icon icon="login" class="w-4 h-4" />

                <span>LOGIN</span>
            </x-buttons.secondary-button>
        </div>
    @endif
    </div>

    <!-- Mobile View -->
    <div class="lg:hidden flex justify-center items-center gap-[8px]">
        <x-buttons.secondary-button onclick="Livewire.dispatch('modal.open', {component: 'cart.cart-show'})"
                                    class="!px-0 !py-0 items-center justify-center w-[39px] h-[39px]">
            <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M2.94046 2.52519C3.01803 2.50228 3.09936 2.4949 3.17979 2.50345C3.26022 2.51201 3.33817 2.53634 3.40919 2.57505C3.48021 2.61376 3.5429 2.6661 3.59367 2.72906C3.64445 2.79202 3.68231 2.86438 3.7051 2.94199L4.15634 4.46973H16.1798C17.6894 4.46973 18.8421 5.8785 18.4072 7.34963L17.0494 11.9443C16.7606 12.924 15.8426 13.5771 14.8219 13.5771H7.31008C6.28946 13.5771 5.37222 12.924 5.0826 11.9443L2.52532 3.2907C2.479 3.13424 2.49671 2.96579 2.57456 2.82239C2.65241 2.67899 2.78402 2.57156 2.94046 2.52519ZM5.78161 16.6539C5.78161 16.1643 5.9761 15.6947 6.32228 15.3485C6.66847 15.0023 7.138 14.8078 7.62758 14.8078C8.11716 14.8078 8.58669 15.0023 8.93288 15.3485C9.27907 15.6947 9.47355 16.1643 9.47355 16.6539C9.47355 17.1435 9.27907 17.6131 8.93288 17.9593C8.58669 18.3055 8.11716 18.5 7.62758 18.5C7.138 18.5 6.66847 18.3055 6.32228 17.9593C5.9761 17.6131 5.78161 17.1435 5.78161 16.6539ZM12.3451 16.6539C12.3451 16.4115 12.3928 16.1714 12.4856 15.9474C12.5783 15.7235 12.7143 15.52 12.8857 15.3485C13.0571 15.1771 13.2606 15.0411 13.4846 14.9483C13.7086 14.8556 13.9486 14.8078 14.191 14.8078C14.4335 14.8078 14.6735 14.8556 14.8975 14.9483C15.1214 15.0411 15.3249 15.1771 15.4963 15.3485C15.6677 15.52 15.8037 15.7235 15.8965 15.9474C15.9893 16.1714 16.037 16.4115 16.037 16.6539C16.037 17.1435 15.8425 17.6131 15.4963 17.9593C15.1501 18.3055 14.6806 18.5 14.191 18.5C13.7015 18.5 13.2319 18.3055 12.8857 17.9593C12.5395 17.6131 12.3451 17.1435 12.3451 16.6539Z"
                    fill="#5F6C87" />
            </svg>
            </x-buttons>

            <x-buttons.secondary-button class="!px-0 !py-0 items-center justify-center w-[39px] h-[39px]">
                <svg viewBox="0 0 19 19" width="18" height='18' xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M14 1.25H5C4.0058 1.25119 3.05267 1.64666 2.34966 2.34966C1.64666 3.05267 1.25119 4.0058 1.25 5V11C1.25109 11.8642 1.55007 12.7015 2.09654 13.3709C2.64301 14.0404 3.40356 14.5009 4.25 14.675V17C4.24998 17.1358 4.28682 17.269 4.35659 17.3855C4.42637 17.502 4.52645 17.5973 4.64618 17.6614C4.7659 17.7255 4.90077 17.7558 5.03639 17.7492C5.17201 17.7426 5.3033 17.6994 5.41625 17.624L9.725 14.75H14C14.9942 14.7488 15.9473 14.3533 16.6503 13.6503C17.3533 12.9473 17.7488 11.9942 17.75 11V5C17.7488 4.0058 17.3533 3.05267 16.6503 2.34966C15.9473 1.64666 14.9942 1.25119 14 1.25ZM12.5 10.25H6.5C6.30109 10.25 6.11032 10.171 5.96967 10.0303C5.82902 9.88968 5.75 9.69891 5.75 9.5C5.75 9.30109 5.82902 9.11032 5.96967 8.96967C6.11032 8.82902 6.30109 8.75 6.5 8.75H12.5C12.6989 8.75 12.8897 8.82902 13.0303 8.96967C13.171 9.11032 13.25 9.30109 13.25 9.5C13.25 9.69891 13.171 9.88968 13.0303 10.0303C12.8897 10.171 12.6989 10.25 12.5 10.25ZM14 7.25H5C4.80109 7.25 4.61032 7.17098 4.46967 7.03033C4.32902 6.88968 4.25 6.69891 4.25 6.5C4.25 6.30109 4.32902 6.11032 4.46967 5.96967C4.61032 5.82902 4.80109 5.75 5 5.75H14C14.1989 5.75 14.3897 5.82902 14.5303 5.96967C14.671 6.11032 14.75 6.30109 14.75 6.5C14.75 6.69891 14.671 6.88968 14.5303 7.03033C14.3897 7.17098 14.1989 7.25 14 7.25Z"
                        fill="#5F6C87" />
                </svg>

                </x-buttons>
                <x-buttons.secondary-button onclick="Livewire.dispatch('modal.open', { component: 'hamburger-modal'})"
                                            class="!px-0 !py-0 items-center justify-center w-[39px] h-[39px]">

                    <svg width="25" height="25" viewBox="0 0 25 25" fill="none"
                         xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.29688 7.42188H20.7031M4.29688 12.5H20.7031M4.29688 17.5781H20.7031" stroke="#5F6C87"
                              stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" />
                    </svg>


                    </x-buttons>
    </div>


</header>
