<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? 'Home' }} - mydrop24.com</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Styles -->
    @livewireStyles
    @turnstileScripts
</head>
<body class="bg-midnight text-white font-sans antialiased">
    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 relative" role="alert">
        <strong class="font-bold">Warning!</strong>
        <span class="block sm:inline"><strong>BETA</strong> - Currently, the USD balance has no real value.</span>
        <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
      </span>
    </div>
    <livewire:toasts />

    <div class="flex flex-col min-h-screen">
        @include('components.layouts.partials.header')

        <div class="px-2 flex flex-1 flex-col md:flex-row w-full">
            @include('components.layouts.partials.sidebar')

            <div class="flex flex-col flex-grow mt-5 my-2 md:mr-4">
                <main class="flex-grow">
                    {{ $slot }}

                    <div class="flex items-center bg-midnight sticky bottom-0  py-[9px] md:hidden">
                        @if (!auth()->check())
                            <div class="flex w-full gap-4 justify-center font-light">
                                <x-buttons.primary-button
                                    onclick="Livewire.dispatch('modal.open', { component: 'auth.authentication-modal' })"
                                    class="gap-2 w-1/2 max-w-[183px] h-[50px] justify-center text-nowrap">
                                    <x-svg-icon icon="login" class="w-4 h-4" />

                                    <span>SIGN UP</span>
                                </x-buttons.primary-button>
                                <x-buttons.secondary-button
                                    onclick="Livewire.dispatch('modal.open', { component: 'auth.authentication-modal', arguments: { isLogin: true } })"
                                    class="gap-2 w-1/2 max-w-[183px] h-[50px] justify-center">
                                    <x-svg-icon icon="login" class="w-4 h-4" />

                                    <span>LOGIN</span>
                                </x-buttons.secondary-button>
                            </div>
                        @endif
                    </div>
                </main>

                @include('components.layouts.partials.footer')
            </div>

        </div>
    </div>

    @filepondScripts
    @livewire('modal-pro')
    @livewireScripts
    @if (auth()->guest() && request()->query('login') == "1")
        <script>
            document.addEventListener("DOMContentLoaded", () => {
                Livewire.dispatch('modal.open', { component: 'auth.authentication-modal', arguments: { isLogin: true } })
            });
        </script>
    @endif
</body>
</html>
