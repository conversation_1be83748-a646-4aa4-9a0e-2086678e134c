<a href="{{ route('boxes.show', ['box' => $box]) }}" wire:navigate class="mx-auto group relative">
    <div class="flex flex-col justify-center items-start p-2">
        <img src="{{ $box->getFirstMedia('image')->getTemporaryUrl(\Carbon\Carbon::now()->addMinutes(5), 'thumb') }}" alt="{{ $box->name }}" class="group-hover:scale-110 group-hover:-rotate-6 transition-transform duration-300 mx-auto w-full h-full">
        <div class="bg-[#1E1F35] mx-auto text-xs text-white font-bold py-2 px-4 rounded-lg mb-2 border-2 border-solid border-[#25263D]">
            {{ money($box->price) }}
        </div>
        <div class="text-xs mx-auto text-center font-semibold uppercase text-[#9597B6]">{{ $box->name }}</div>
        <div class="absolute inset-0 flex items-center justify-center">
            <div class="rounded-lg w-16 h-16 bg-white bg-opacity-5 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                <x-lucide-search class="w-8 h-8 " />
            </div>
        </div>
    </div>
</a>
