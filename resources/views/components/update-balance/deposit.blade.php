@props([
    'depositMethod'
])

<div class="mt-2">
    <div class="w-full flex content-center">
        <span class="m-auto isolate inline-flex rounded-md shadow-sm">
            <button wire:click="setDepositMethod('prepaid')" type="button" @class([
                "relative inline-flex items-center rounded-l-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10",
                "bg-white" => $depositMethod !== 'prepaid',
                "bg-gray-200" => $depositMethod === 'prepaid',
            ])>Prepaid</button>
            <button wire:click="setDepositMethod('btc')" type="button" @class([
                "relative -ml-px inline-flex items-center rounded-r-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10",
                "bg-white" => $depositMethod !== 'btc',
                "bg-gray-200" => $depositMethod === 'btc',
            ])>Bitcoin</button>
        </span>
    </div>
    <div>
        @if ($depositMethod === 'btc')
            @livewire('deposit.btc')
        @else
            @livewire('deposit.prepaid')
        @endif
    </div>
</div>
