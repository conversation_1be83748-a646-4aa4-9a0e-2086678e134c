<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Cart') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if ($cart->availableItems->count() <= 0)
                    <h2 class="text-lg font-medium text-gray-900">
                        {{ __('You have no items yet ...') }}
                    </h2>
                    @else

                                    <ul role="list" class="divide-y divide-gray-200 border-b border-t border-gray-200">
                                        @foreach($cart->availableItems as $item)
                                            <li class="flex py-6 sm:py-10">
                                                <div class="flex-shrink-0">
                                                    <img src="{{ $item->getFirstMedia('image')->getTemporaryUrl(\Carbon\Carbon::now()->addMinutes(5), 'thumb') }}" alt="{{ $item->name }}" class="h-24 w-24 rounded-lg object-contain object-center sm:h-32 sm:w-32">
                                                </div>

                                                <div class="relative ml-4 flex flex-1 flex-col justify-between sm:ml-6">
                                                    <div>
                                                        <div class="flex justify-between sm:grid sm:grid-cols-2">
                                                            <div class="pr-6">
                                                                <h3 class="text-sm">
                                                                    <a href="#" class="font-medium text-gray-700 hover:text-gray-800">{{ $item->name }}</a>
                                                                </h3>
                                                            </div>
                                                            <div class="flex space-x-3">
                                                                <p class="text-right text-sm font-medium text-gray-900">{{ $item->priceMoney }}</p>
                                                                <div>
                                                                    <x-primary-button onclick="Livewire.dispatch('openModal', { component: 'modals.redeem-item-to-balance', arguments: { cartItemId: {{ $item->pivot->id }} }})">Move to Balance</x-primary-button>
                                                                    @if ($item->shippable)
                                                                        <x-primary-button onclick="Livewire.dispatch('openModal', { component: 'modals.redeem-item-to-shipping', arguments: { cartItemId: {{ $item->pivot->id }} }})">Ship</x-primary-button>
                                                                    @endif
                                                                    <x-primary-button onclick="Livewire.dispatch('openModal', { component: 'modals.redeem-item-via-crypto', arguments: { cartItemId: {{ $item->pivot->id }} }})">Crypto Payout</x-primary-button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>

                                        @endforeach

                                    </ul>

                    @endif

                </div>
            </div>
        </div>
    </div>
</x-app-layout>
