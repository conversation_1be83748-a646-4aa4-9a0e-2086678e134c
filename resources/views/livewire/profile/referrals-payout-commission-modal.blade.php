<div class="rounded-md bg-[linear-gradient(338deg,#11172A_-1.69%,#11172A_85.79%,#3C4E90_140.75%)] px-[22px] py-[25px]">

    <div class="flex justify-between">
        <div class="flex gap-[1px] overflow-hidden rounded-[14px]">
            <x-buttons.tab-item wire:click="selectPayoutType('balance')" :is-active="$payout_type === \App\Enums\PayoutType::BALANCE->value()">{{ \App\Enums\PayoutType::BALANCE->label() }}</x-buttons.tab-item>
            <x-buttons.tab-item wire:click="selectPayoutType('crypto')" :is-active="$payout_type === \App\Enums\PayoutType::CRYPTO->value()">{{ \App\Enums\PayoutType::CRYPTO->label() }}</x-buttons.tab-item>
        </div>
    </div>

    <section>
        <form class="space-y-6" wire:submit="payout">
            @if ($payout_type === \App\Enums\PayoutType::CRYPTO->value())
                <div wire:transition class="mt-5">
                    <div>
                        <x-input-label for="crypto_type" :value="__('Promo-Code')"/>
                        <x-input.select wire:model="selected_crypto_type" id="crypto_type" name="crypto_type" type="text"
                                        class="mt-1 block w-full"
                                        :error="$errors->first('selected_crypto_type')">
                            @foreach($crypto_types as $crypto_type)
                                <option value="{{ $crypto_type[0] }}">{{ $crypto_type[1] }}</option>
                            @endforeach
                        </x-input.select>
                    </div>
                    <div class="mt-3">
                        <x-input-label for="crypto_address" :value="__('Crypto Address')"/>
                        <x-input.text-input wire:model="crypto_address" id="crypto_address" name="crypto_address" type="text"
                                            class="mt-1 block w-full"
                                            :error="$errors->first('crypto_address')"
                        />
                    </div>
                </div>
            @endif
            <div class="mt-3">
                <x-input-label for="amount" :value="__('Amount')"/>
                <x-input.text-input wire:model="amount" id="amount" name="amount" type="number" min="0.00" step="0.01" class="mt-1 block w-full" required
                                    autocomplete="amount"
                                    :error="$errors->first('amount')"
                />
            </div>

            <div>
                <x-buttons.base-button type="submit">{{ __('Payout') }}</x-buttons.base-button>
            </div>
        </form>
    </section>

</div>
