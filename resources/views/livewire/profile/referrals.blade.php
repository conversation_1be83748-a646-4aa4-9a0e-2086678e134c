<?php

use App\Livewire\Actions\Logout;
use Illuminate\Support\Facades\Auth;
use Livewire\Volt\Component;

new class extends Component
{
    public array $referrals = [];

    public function mount(): void
    {
        $this->referrals = Auth::user()->referrals->toArray();
    }

}; ?>



<section class="space-y-6">
    <header>
        <h2 class="text-lg font-medium text-gray-900">
            {{ __('Your referrals') }}
        </h2>

        <p class="mt-1 text-sm text-gray-600">
            {{ __('You can see you successful assigned referrals') }}
        </p>
    </header>


    <div class="flex space-x-1.5">
        @if (empty($referrals))
            <p>{{ __('You have no referrals yet') }}</p>
        @else
            <table class="table-auto">
                <thead>
                <tr>
                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Name</th>
                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">E-Mail</th>
                </tr>
                </thead>
                <tbody>
                    @foreach($referrals as $referral)
                        <tr>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $referral['name'] }}</td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $referral['email'] }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @endif
    </div>


</section>
