<?php

use App\Models\User;
use CountryEnums\Country;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rule;
use Livewire\Volt\Component;

new class extends Component {
    public string $first_name = '';
    public string $last_name = '';
    public string $address = '';
    public string $address_two = '';
    public string $postcode = '';
    public string $state = '';
    public string $city = '';
    public string $country = '';
    public string $phone = '';

    public array $countries = [];

    /**
     * Mount the component.
     */
    public function mount(): void
    {
        $user = Auth::user();
        $this->countries = Country::getOptions();

        if ($user->address()->exists()) {
            $this->first_name = $user->address->first_name;
            $this->last_name = $user->address->last_name;
            $this->address = $user->address->address;
            $this->address_two = $user->address->address_two;
            $this->postcode = $user->address->postcode;
            $this->state = $user->address->state;
            $this->city = $user->address->city;
            $this->country = $user->address->country;
            $this->phone = $user->address->phone;
        }
    }

    /**
     * Update the profile information for the currently authenticated user.
     */
    public function updateShippingAddress(): void
    {
        $user = Auth::user();

        $updateShippingAddress = \App\Actions\User\UpdateShippingAddress::make();

        $validated = $this->validate($updateShippingAddress->rules());

        \App\Actions\User\UpdateShippingAddress::run($user, $validated);

        $this->dispatch('shipping-address-updated', user_id: $user->id);
    }

}; ?>

<section>
    <form wire:submit="updateShippingAddress" class="mt-6 space-y-6">
        <div class="flex flex-col gap-4 md:flex-row justify-between md:space-x-4">
            <div class="w-full">
                <x-input-label for="first_name" :value="__('FIRST NAME')"/>
                <x-input.text-input wire:model="first_name" id="first_name" name="first_name" type="text"
                                    class="mt-1 block w-full" autofocus autocomplete="first_name" :error="$errors->first('first_name')" />
            </div>

            <div class="w-full">
                <x-input-label for="last_name" :value="__('LAST NAME')"/>
                <x-input.text-input wire:model="last_name" id="last_name" name="last_name" type="text"
                                    class="mt-1 block w-full" autocomplete="last_name" :error="$errors->first('last_name')" :error="$errors->first('last_name')" />
            </div>
        </div>

        <div class="flex flex-col md:flex-row justify-between gap-4 md:space-x-4">
            <div class="w-full">
                <x-input-label for="address" :value="__('ADDRESS 1')"/>
                <x-input.text-input wire:model="address" id="address" name="address" type="text" class="mt-1 block w-full"
                                    autofocus autocomplete="address" :error="$errors->first('address')" />
            </div>

            <div class="w-full">
                <x-input-label for="address_two" :value="__('ADDRESS 2')"/>
                <x-input.text-input wire:model="address_two" id="address_two" name="address_two" type="text"
                                    class="mt-1 block w-full" autocomplete="address_two" :error="$errors->first('address_two')" />
            </div>
        </div>

        <div class="flex flex-col md:flex-row gap-4 justify-between md:space-x-4">
            <div class="w-full">
                <x-input-label for="postcode" :value="__('POSTAL / ZIP CODE')"/>
                <x-input.text-input wire:model="postcode" id="postcode" name="postcode" type="text" class="mt-1 block w-full"
                                    autofocus autocomplete="postcode" :error="$errors->first('postcode')" />
            </div>

            <div class="w-full">
                <x-input-label for="state" :value="__('STATE / PROVINCE / REGION')"/>
                <x-input.text-input wire:model="state" id="state" name="state" type="text" class="mt-1 block w-full"
                                    autocomplete="state" :error="$errors->first('state')" />
            </div>
        </div>

        <div class="flex flex-col md:flex-row gap-4 justify-between md:space-x-4">
            <div class="w-full">
                <x-input-label for="city" :value="__('CITY')"/>
                <x-input.text-input wire:model="city" id="city" name="city" type="text" class="mt-1 block w-full"
                                    autofocus autocomplete="city" :error="$errors->first('city')" />
            </div>

            <div class="w-full">
                <x-input-label for="country" :value="__('COUNTRY')"/>
                <x-input.select class="mt-1 block w-full" wire:model="country" id="country" name="country" :error="$errors->first('country')">
                    <option value="">Please Select</option>
                    <option value="" disabled>------------</option>
                    @foreach($countries as $key => $country)
                        <option value="{{ $key }}">{{ $country }}</option>
                    @endforeach
                </x-input.select>
            </div>
        </div>

        <div class="flex-1">
            <div>
                <x-input-label for="phone" :value="__('PHONE (INCLUDING COUNTRY CODE)')"/>
                <x-input.text-input wire:model="phone" id="phone" name="phone" type="text" class="mt-1 block w-full"
                                    autocomplete="phone" :error="$errors->first('phone')" />
            </div>
        </div>

        <div class="flex items-center gap-4">
            <x-buttons.base-button>{{ __('Save') }}</x-buttons.base-button>

            <x-action-message class="me-3" on="shipping-address-updated">
                {{ __('Saved.') }}
            </x-action-message>
        </div>
    </form>
</section>
