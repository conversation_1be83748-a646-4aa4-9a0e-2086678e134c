<div
    class="flex justify-between gap-2 flex-wrap w-full rounded-2xl border-2 border-solid border-[#0B172D] bg-[#060D21] text-xs">
    <a href="{{ route('profile.index') }}" @class([
        'flex items-center justify-center gap-2.5 rounded-xl px-3 py-3 text-[#DADCF3]',
        'bg-[linear-gradient(180deg,#0D1428_0%,#222B45_100%)]' => request()->routeIs(
            'profile.index'),
    ]) wire:navigate>
        <x-svg-icon icon="profile" class="w-5 h-5" />
        <span class="font-bold">PROFILE</span>
    </a>
    <a href="{{ route('profile.unbox-history') }}" @class([
        'flex items-center justify-center gap-2.5 rounded-xl px-3 py-3 text-[#DADCF3]',
        'bg-[linear-gradient(180deg,#0D1428_0%,#222B45_100%)]' => request()->routeIs(
            'profile.unbox-history'),
    ]) wire:navigate>
        <x-svg-icon icon="history" class="w-5 h-5" />
        <span class="font-bold">UNBOX HISTORY</span>
    </a>
    <a href="{{ route('profile.referral') }}" @class([
        'flex items-center justify-center gap-2.5 rounded-xl px-3 py-3 text-[#DADCF3]',
        'bg-[linear-gradient(180deg,#0D1428_0%,#222B45_100%)]' => request()->routeIs(
            'profile.referral'),
    ]) wire:navigate>
        <x-svg-icon icon="referral" class="w-5 h-5" />
        <span class="font-bold">REFERRAL</span>
    </a>
    <a href="{{ route('profile.transaction-history') }}" @class([
        'flex items-center justify-center gap-2.5 rounded-xl px-3 py-3 text-[#DADCF3]',
        'bg-[linear-gradient(180deg,#0D1428_0%,#222B45_100%)]' => request()->routeIs(
            'profile.transaction-history'),
    ]) wire:navigate>
        <x-svg-icon icon="history" class="w-5 h-5" />
        <span class="font-bold">TRANSACTION HISTORY</span>
    </a>

</div>
