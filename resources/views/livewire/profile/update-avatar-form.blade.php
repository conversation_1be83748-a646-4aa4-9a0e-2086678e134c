<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\ValidationException;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Livewire\Volt\Component;
use Spatie\LivewireFilepond\WithFilePond;

new class extends Component {
    use WithFilePond;

    public ?TemporaryUploadedFile $avatar = null;

    /**
     * Update the password for the currently authenticated user.
     */
    public function uploadAvatar(): void
    {
        $this->validate([
            'avatar' => ['required', 'file', 'image', 'max:5120'],
        ]);

        auth()->user()->addMediaFromDisk($this->avatar->getRealPath())->toMediaCollection('avatar');

        $this->avatar = null;

        $this->dispatch('profile.avatar.updated');
    }
}; ?>

<section>
    <form wire:submit="uploadAvatar" class="mt-6 space-y-6">
        <x-filepond::upload wire:model="avatar" :credits="false" />

        @if ($errors->has('avatar'))
            <p class="w-full mt-2 text-sm text-red-600">{{ $errors->first('avatar') }}</p>
        @endif

        <div class="flex items-center gap-4">
            <x-buttons.base-button>{{ __('Save') }}</x-buttons.base-button>

            <x-action-message class="me-3" on="profile.avatar.updated">
                {{ __('Saved.') }}
            </x-action-message>
        </div>
    </form>
</section>
