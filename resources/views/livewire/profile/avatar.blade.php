<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\ValidationException;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Livewire\Volt\Component;
use Spatie\LivewireFilepond\WithFilePond;

new class extends Component {

    public $avatar;

    /**
     * Update the password for the currently authenticated user.
     */
    public function mount(): void
    {
        $this->loadAvatar();
    }

    #[\Livewire\Attributes\On('profile.avatar.updated')]
    public function loadAvatar(): void
    {
        $this->avatar = auth()->user()->avatar;
    }
}; ?>

<div class="w-11 relative flex aspect-square items-center justify-center rounded-full border-[2px] border-[#1E61ED]">
    <img alt="avatar" class="rounded-full" src="{{ $avatar ?? Vite::asset('resources/images/avatar/avatar.png') }}">
    <img alt="rays" class="absolute opacity-40 scale-[2.5]" src="{{ Vite::asset('resources/images/avatar/rays.svg') }}">
    <img alt="rays" class="absolute opacity-30 scale-[2.5]" src="{{ Vite::asset('resources/images/avatar/circle.svg') }}">

    <div class="absolute top-full flex h-[14px] w-[14px] -translate-y-1/2 items-center justify-center rounded-full border-[2px] border-[#13161F] bg-[#1E61ED]">
        <svg fill="none" height="6" viewBox="0 0 6 6" width="6" xmlns="http://www.w3.org/2000/svg">
            <path d="M3.14694 4.50013L5.54996 2.09707C5.60559 2.04149 5.63623 1.96729 5.63623 1.88818C5.63623 1.80907 5.60559 1.73487 5.54996 1.67929L5.37304 1.50232C5.25775 1.38717 5.07038 1.38717 4.95527 1.50232L2.93735 3.52024L0.917195 1.50009C0.861571 1.44451 0.78742 1.41382 0.708353 1.41382C0.629198 1.41382 0.555047 1.44451 0.499379 1.50009L0.322497 1.67706C0.266873 1.73268 0.23623 1.80683 0.23623 1.88594C0.23623 1.96505 0.266873 2.03925 0.322497 2.09483L2.72772 4.50013C2.78352 4.55585 2.85802 4.58645 2.93722 4.58627C3.01672 4.58645 3.09118 4.55585 3.14694 4.50013Z" fill="white"></path>
        </svg>
    </div>
</div>
