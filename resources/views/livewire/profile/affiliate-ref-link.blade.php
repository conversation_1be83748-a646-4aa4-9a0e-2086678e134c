<?php

use App\Livewire\Actions\Logout;
use Illuminate\Support\Facades\Auth;
use Livewire\Volt\Component;

new class extends Component
{
    public string $ref_link = '';

    public function mount()
    {
        $this->ref_link = Auth::user()->getAffiliateLink();
    }

    public function copyToClipboard(): void
    {
        $this->dispatch('affiliate-link-copied');
    }

}; ?>



<section class="space-y-6">
    <header>
        <h2 class="text-lg font-medium text-gray-900">
            {{ __('Your affiliate link') }}
        </h2>

        <p class="mt-1 text-sm text-gray-600">
            {{ __('Share your affiliate link and earn great benefits!') }}
        </p>
    </header>


    <div class="flex space-x-1.5" x-data="{ ref_link: $wire.entangle('ref_link'), copied: false }">
        <x-text-input x-model="ref_link" wire:model="ref_link" id="ref" name="ref" type="text" class="block w-full" readonly />

        <x-secondary-button x-text="copied ? 'Copied' : 'Copy'" @click="$clipboard(ref_link); copied = true; setTimeout(() => { copied = false }, 2000);">
        </x-secondary-button>
    </div>

    <hr>

    @if (Auth::user()->referredBy)
        <p>{{ __('You were referred by') }} {{ Auth::user()->referredBy->name }} ({{ Auth::user()->referredBy->email }})</p>
    @else
        <p>{{ __('You were not referred by anyone') }}</p>
    @endif


</section>
