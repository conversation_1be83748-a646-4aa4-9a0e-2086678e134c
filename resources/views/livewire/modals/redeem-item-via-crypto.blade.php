<div class="bg-white p-2">
    <div class="sm:mx-auto sm:w-full sm:max-w-sm">
        <h2 class="text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">Redeem Item via crypto</h2>
        <p>
            Are you sure you want to payout this item {{ $cartItem->item->name }} via Crypto?
        </p>
    </div>

    <div class="mt-10 mb-10 sm:mx-auto sm:w-full sm:max-w-sm">
        <form class="space-y-6" wire:submit="redeem">
            <div>
                <x-input-label for="crypto_type" :value="__('Crypto Type')" />
                <x-select wire:model="cryptoType" id="crypto_type" name="crypto_type">
                    <option value="" selected>Please Select</option>
                    <option disabled>------------</option>
                    @foreach($crypto_types as $crypto_type)
                        <option value="{{ $crypto_type[0] }}">{{ $crypto_type[1] }}</option>
                    @endforeach
                </x-select>
                <x-input-error :messages="$errors->get('cryptoType')" class="mt-2" />
            </div>
            <div>
                <x-input-label for="crypto_address" :value="__('Crypto Address')" />
                <x-text-input wire:model="cryptoAddress" id="crypto_address" class="block mt-1 w-full" type="text" name="crypto_address" required autofocus />
                <x-input-error :messages="$errors->get('cryptoAddress')" class="mt-2" />
            </div>
            <div>
                <x-primary-button type="submit" class="w-full text-center">Redeem</x-primary-button>
            </div>
            <div>
                <x-secondary-button wire:click="$dispatch('closeModal')">Cancel</x-secondary-button>
            </div>
        </form>
    </div>
</div>
