<x-modals.headless>
        <!-- Header -->
        <div class="flex items-center justify-center mb-4">
            <h1 class="text-lg font-black text-center">SELECT CASES</h1>
        </div>

        <!-- Cases Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 overflow-y-auto max-h-[65vh]">
            <!-- Case Card -->
            @foreach($availableBoxes as $box)
                <div class="bg-[#060D21] p-3 rounded-[20px] border-[1px] border-[#0B172D] flex flex-col items-center justify-center">
                    <div class="relative">
                        <img src="{{ $box->getFirstMedia('image')->getTemporaryUrl(\Carbon\Carbon::now()->addMinutes(5), 'thumb') }}" alt="{{ $box->name }}" class="mx-auto">
                        @if ($this->isSelected($box->id))
                            <button wire:click="removeBox('{{ $box->id }}')" class="absolute top-1 right-1 text-gray-400 hover:text-red-500">
                                <x-lucide-trash-2 class="w-4 text-[#9597B6] opacity-40" />
                            </button>
                        @endif
                    </div>
                    <div class="text-center mt-4 mb-4">
                        <h3 class="text-xs text-[#9597B6] font-medium uppercase">{{ $box->name }}</h3>
                        <p class="text-base font-black mt-2">{{ money($box->price) }}</p>
                    </div>

                    @if ($this->isSelected($box->id))
                        <div class="flex items-center justify-between mt-4 bg-[#1E1F35] p-2 rounded-md w-10/12">
                            <button wire:click="decreaseAmount('{{ $box['id'] }}')" class="w-8 h-8 bg-[#060D21] text-white font-bold rounded-md hover:bg-blue-500">-</button>
                            <span class="text-white font-bold">{{ $selectedBoxIds[$box['id']] }}</span>
                            <button wire:click="increaseAmount('{{ $box['id'] }}')" class="w-8 h-8 bg-[#060D21] text-white font-bold rounded-md hover:bg-blue-500">+</button>
                        </div>
                    @else
                        <x-buttons.primary-button wire:click="addBox('{{ $box->id }}')">Add Case</x-buttons.primary-button>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- Footer -->
        <div class="flex justify-between mt-6 p-6 border-t-[1px] border-[#111C30] text-[#62768E]">
            <div class="w-full">
                <div class="text-[13px] font-medium">Total Cases</div>
                <div class="text-base"><span class="text-white">{{ $this->totalCases }}</span>/{{ \App\Livewire\Pages\Battles\BattlesCreate::$MAX_ROUNDS }}</div>
            </div>
            <div class="text-right w-full">
                <div class="font-medium text-[15px]">Total Cost: <span class="text-primary-500">{{ money($this->totalCost) }}</span></div>
                <div class="text-xs flex justify-end">Your current balance  <livewire:balance /></div>
            </div>
        </div>

</x-modals.headless>
