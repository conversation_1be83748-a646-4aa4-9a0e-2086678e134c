<div class="rounded-md bg-[linear-gradient(338deg,#11172A_-1.69%,#11172A_85.79%,#3C4E90_140.75%)] px-[22px] py-[25px]">
    <h2 class="text-[20px] font-bold text-white text-center">
        Create Referral Code
    </h2>

    <div>
        <form wire:submit="save">
            <div class="mt-[20px] flex w-full flex-col gap-3">
                <x-input.text-input
                    wire:model="promoCode"
                    type="text"
                    placeholder="PROMO"
                    autofocus
                    :error="$errors->first('promoCode')"
                />
            </div>

            <button type="submit" class="flex h-[50px] justify-center duration-300 items-center gap-3 px-5 py-[15px] rounded-xl text-xs font-bold uppercase bg-[#4046C1] hover:bg-[#5359d8] text-white mt-[22px] w-full">Create</button>
        </form>
    </div>
</div>
