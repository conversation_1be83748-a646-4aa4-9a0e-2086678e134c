<div class="flex max-h-[580px]">
    <div class="relative flex h-[580px] w-[327px] shrink-0 flex-col bg-[#1B1E3A] px-[34px] py-[30px]">
        <div class="relative z-10 mt-auto flex items-center justify-center gap-2 text-white">
            <svg fill="none" height="29" viewBox="0 0 29 29" width="29" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_220_1598)">
                    <path d="M14.802 12.9761L12.1424 12.0895L14.802 11.203L17.4617 12.0895L14.802 12.9761Z" fill="white"></path>
                    <path clip-rule="evenodd" d="M27.3008 0.849609C27.3008 0.380625 27.6814 0 28.1504 0C28.6194 0 29 0.380625 29 0.849609V3.58875C29 4.0583 28.6194 4.43836 28.1504 4.43836C27.6814 4.43836 27.3008 4.0583 27.3008 3.58875V3.06879H25.6016V21.5789C25.6016 22.0485 25.2209 22.4286 24.752 22.4286C24.283 22.4286 23.9023 22.0485 23.9023 21.5789V20.8177L22.1748 21.9346V23.7851C22.1748 24.2547 21.7942 24.6347 21.3252 24.6347C20.8562 24.6347 20.4756 24.2547 20.4756 23.7851V23.0329L18.748 24.1499V26.0037C18.748 26.4727 18.3674 26.8533 17.8984 26.8533C17.4295 26.8533 17.0488 26.4727 17.0488 26.0037V25.2487L15.3496 26.347V28.1504C15.3496 28.6194 14.969 29 14.5 29C14.031 29 13.6504 28.6194 13.6504 28.1504V26.347L11.9512 25.2487V26.0037C11.9512 26.4727 11.5705 26.8533 11.1016 26.8533C10.6326 26.8533 10.252 26.4727 10.252 26.0037V24.1499L8.52441 23.0329V23.7851C8.52441 24.2547 8.14379 24.6347 7.67481 24.6347C7.20582 24.6347 6.8252 24.2547 6.8252 23.7851V21.9346L5.09766 20.8177V21.5789C5.09766 22.0485 4.71703 22.4286 4.24805 22.4286C3.77906 22.4286 3.39844 22.0485 3.39844 21.5789V3.06879H1.69922V3.58875C1.69922 4.0583 1.31859 4.43836 0.849609 4.43836C0.380625 4.43836 0 4.0583 0 3.58875V0.849609C0 0.380625 0.380625 0 0.849609 0C1.31859 0 1.69922 0.380625 1.69922 0.849609V1.36957H3.39844V0.849609C3.39844 0.380625 3.77906 0 4.24805 0H8.34883C8.81781 0 9.19844 0.380625 9.19844 0.849609V1.36957H11.6V0.849609C11.6 0.380625 11.9806 0 12.4496 0H16.5504C17.0194 0 17.4 0.380625 17.4 0.849609V1.36957H19.8016V0.849609C19.8016 0.380625 20.1822 0 20.6512 0H24.752C25.2209 0 25.6016 0.380625 25.6016 0.849609V1.36957H27.3008V0.849609ZM19.8016 3.06879H17.4V3.56723C17.4 4.22936 17.9387 4.76801 18.6008 4.76801C19.2629 4.76801 19.8016 4.22936 19.8016 3.56723V3.06879ZM11.6 3.06879H9.19844V3.56723C9.19844 4.22936 9.73709 4.76801 10.3992 4.76801C11.0613 4.76801 11.6 4.22936 11.6 3.56723V3.06879ZM19.8869 13.728L18.7745 12.0895L19.8869 10.451C19.9125 10.4125 19.9289 10.3686 19.9349 10.3228C19.9409 10.2769 19.9362 10.2303 19.9212 10.1866C19.8907 10.0978 19.8207 10.0282 19.7317 9.99879L16.1008 8.79519C15.971 8.75139 15.8305 8.80076 15.7553 8.9136L14.802 10.3435L13.8488 8.9136C13.773 8.80018 13.6313 8.75109 13.5032 8.79519L9.87231 9.99879C9.78329 10.0282 9.71334 10.0978 9.68279 10.1866C9.66784 10.2303 9.66317 10.2769 9.66913 10.3228C9.67509 10.3687 9.69153 10.4125 9.71717 10.451L10.8296 12.0895L9.71719 13.728C9.69155 13.7665 9.67511 13.8104 9.66914 13.8562C9.66318 13.9021 9.66786 13.9487 9.68281 13.9925C9.71336 14.0812 9.78331 14.1509 9.87233 14.1803L13.5032 15.3839C13.631 15.4268 13.7731 15.3789 13.8488 15.2654L14.802 13.8356L15.7553 15.2654C15.831 15.379 15.9732 15.4268 16.1009 15.3839L19.7317 14.1803C19.8208 14.1509 19.8907 14.0812 19.9213 13.9925C19.9362 13.9487 19.9409 13.9021 19.9349 13.8562C19.929 13.8104 19.9125 13.7665 19.8869 13.728ZM16.2913 15.9548C15.9125 16.0819 15.4843 15.9441 15.2546 15.5992L14.8021 14.9205L14.3495 15.5992C14.1202 15.9434 13.6925 16.0825 13.3113 15.9542L10.8903 15.1473V16.9039C10.8903 17.0335 10.9732 17.1484 11.096 17.1893L14.7051 18.3923C14.7159 18.3961 14.7268 18.399 14.7377 18.4014C14.7377 18.4013 14.7378 18.4014 14.7377 18.4014C14.7588 18.4058 14.7809 18.4085 14.8021 18.4085C14.8349 18.4084 14.8676 18.403 14.8987 18.3926L18.5081 17.1893C18.568 17.1693 18.6201 17.1311 18.657 17.0798C18.6939 17.0286 18.7138 16.9671 18.7137 16.9039V15.1473L16.2913 15.9548Z" fill-rule="evenodd" fill="white"></path>
                </g>
                <defs>
                    <clipPath id="clip0_220_1598">
                        <rect fill="white" height="29" width="29"></rect>
                    </clipPath>
                </defs>
            </svg>
            <span class="text-[22px] font-bold text-white">MyDrop24</span>
        </div>

        <img alt="bg" class="absolute inset-0 z-0 h-full w-full object-cover" src="{{ Vite::asset('resources/images/registration/bg.png') }}">
    </div>

    <div class="flex w-[444px] shrink-0 flex-col items-center bg-[#1B1E3A] p-5">
        <h2 class="text-[20px] font-bold text-white">
            Forgot Password
        </h2>

        <div class="w-full">
            <form wire:submit="sendPasswordResetLink">

                <div class="mt-[20px] flex w-full flex-col gap-3">
                    <x-input.text-input wire:model="email" type="email" placeholder="E-Mail" autofocus autocomplete="username" :error="$errors->first('email')" required>
                        <x-slot:icon>
                            <svg fill="none" height="20" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_220_1613)">
                                    <path d="M12.0469 9.84375C10.8837 9.84375 9.9375 10.79 9.9375 11.9531C9.9375 13.1163 10.8837 14.0625 12.0469 14.0625C13.21 14.0625 14.1562 13.1163 14.1562 11.9531C14.1562 10.79 13.21 9.84375 12.0469 9.84375Z" fill="#575B7C"></path>
                                    <path d="M11.9531 0C5.36203 0 0 5.36203 0 11.9531C0 18.5442 5.36203 24 11.9531 24C18.5442 24 24 18.5442 24 11.9531C24 5.36203 18.5442 0 11.9531 0ZM16.9688 15.4688C16.0185 15.4688 15.1804 14.9925 14.6711 14.2689C14.0264 14.9986 13.0947 15.4688 12.0469 15.4688C10.1085 15.4688 8.53125 13.8915 8.53125 11.9531C8.53125 10.0147 10.1085 8.4375 12.0469 8.4375C12.8413 8.4375 13.5668 8.71242 14.1562 9.15858V9.14062C14.1562 8.75198 14.4707 8.4375 14.8594 8.4375C15.248 8.4375 15.5625 8.75198 15.5625 9.14062V12.6562C15.5625 13.4315 16.1935 14.0625 16.9688 14.0625C17.744 14.0625 18.375 13.4315 18.375 12.6562C18.375 7.95042 15.261 5.625 12.0469 5.625C8.55736 5.625 5.71875 8.46361 5.71875 11.9531C5.71875 15.4426 8.55736 18.2812 12.0469 18.2812C13.4531 18.2812 14.7832 17.8308 15.8935 16.978C16.6351 16.4118 17.4844 17.5295 16.7504 18.0931C15.3915 19.1361 13.7655 19.6875 12.0469 19.6875C7.78214 19.6875 4.3125 16.2179 4.3125 11.9531C4.3125 7.68839 7.78214 4.21875 12.0469 4.21875C15.9327 4.21875 19.7812 7.08445 19.7812 12.6562C19.7812 14.2074 18.5199 15.4688 16.9688 15.4688Z" fill="#575B7C"></path>
                                </g>
                                <defs>
                                    <clipPath id="clip0_220_1613">
                                        <rect fill="white" height="24" width="24"></rect>
                                    </clipPath>
                                </defs>
                            </svg>
                        </x-slot:icon>
                    </x-input.text-input>

                </div>

                <button type="submit" class="flex h-[50px] justify-center duration-300 items-center gap-3 px-5 py-[15px] rounded-xl text-xs font-bold uppercase bg-[#4046C1] hover:bg-[#5359d8] text-white mt-[22px] w-full">Send Password link</button>
            </form>

        </div>

    </div>

</div>
