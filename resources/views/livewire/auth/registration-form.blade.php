
<div class="w-full">
    <form wire:submit="register">

        <div class="mt-[20px] flex w-full flex-col gap-3">
            <x-input.text-input wire:model="name" type="text" placeholder="Name" :error="$errors->first('name')">
                <x-slot:icon>
                    <svg fill="none" height="20" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_220_1645)">
                            <path d="M11.6163 11.5609C13.2045 11.5609 14.5798 10.9912 15.7036 9.86729C16.8273 8.74361 17.3969 7.36867 17.3969 5.78027C17.3969 4.19234 16.8273 2.81727 15.7034 1.69317C14.5795 0.569623 13.2043 0 11.6163 0C10.0279 0 8.65289 0.569623 7.52921 1.69335C6.40553 2.81708 5.83567 4.1922 5.83567 5.78027C5.83567 7.36867 6.40548 8.74379 7.5294 9.86752C8.65331 10.9911 10.0284 11.5609 11.6163 11.5609ZM21.7308 18.4548C21.6984 17.9871 21.6329 17.4769 21.5363 16.9383C21.4389 16.3955 21.3135 15.8825 21.1634 15.4136C21.0083 14.9289 20.7974 14.4503 20.5366 13.9916C20.266 13.5155 19.9481 13.1009 19.5914 12.7598C19.2184 12.403 18.7618 12.1161 18.2337 11.9067C17.7075 11.6986 17.1242 11.5931 16.5004 11.5931C16.2554 11.5931 16.0185 11.6936 15.5609 11.9916C15.2357 12.2034 14.9094 12.4137 14.5822 12.6225C14.2678 12.8228 13.8419 13.0105 13.3159 13.1804C12.8026 13.3465 12.2815 13.4308 11.7672 13.4308C11.2529 13.4308 10.7319 13.3465 10.2181 13.1804C9.69262 13.0107 9.26672 12.823 8.95266 12.6227C8.58844 12.39 8.25891 12.1776 7.97307 11.9913C7.51604 11.6934 7.2789 11.5929 7.03393 11.5929C6.40989 11.5929 5.8269 11.6985 5.30083 11.907C4.77311 12.1159 4.31627 12.4028 3.94291 12.76C3.58643 13.1013 3.26838 13.5156 2.9981 13.9916C2.73757 14.4502 2.52664 14.9287 2.37134 15.4138C2.22134 15.8827 2.09595 16.3955 1.99854 16.9383C1.90203 17.4762 1.8365 17.9866 1.80406 18.4553C1.77179 18.927 1.7558 19.3997 1.75611 19.8725C1.75611 21.1254 2.15436 22.1395 2.9397 22.8875C3.71533 23.6257 4.74161 24.0001 5.98965 24.0001H17.5458C18.7938 24.0001 19.8197 23.6258 20.5956 22.8876C21.3811 22.1401 21.7793 21.1257 21.7793 19.8723C21.7792 19.3888 21.7629 18.9118 21.7308 18.4548Z" fill="#575B7C"></path>
                        </g>
                        <defs>
                            <clipPath id="clip0_220_1645">
                                <rect fill="white" height="24" width="24"></rect>
                            </clipPath>
                        </defs>
                    </svg>
                </x-slot:icon>
            </x-input.text-input>

            <x-input.text-input wire:model="email" type="email" placeholder="E-Mail" :error="$errors->first('email')">
                <x-slot:icon>
                    <svg fill="none" height="20" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_220_1613)">
                            <path d="M12.0469 9.84375C10.8837 9.84375 9.9375 10.79 9.9375 11.9531C9.9375 13.1163 10.8837 14.0625 12.0469 14.0625C13.21 14.0625 14.1562 13.1163 14.1562 11.9531C14.1562 10.79 13.21 9.84375 12.0469 9.84375Z" fill="#575B7C"></path>
                            <path d="M11.9531 0C5.36203 0 0 5.36203 0 11.9531C0 18.5442 5.36203 24 11.9531 24C18.5442 24 24 18.5442 24 11.9531C24 5.36203 18.5442 0 11.9531 0ZM16.9688 15.4688C16.0185 15.4688 15.1804 14.9925 14.6711 14.2689C14.0264 14.9986 13.0947 15.4688 12.0469 15.4688C10.1085 15.4688 8.53125 13.8915 8.53125 11.9531C8.53125 10.0147 10.1085 8.4375 12.0469 8.4375C12.8413 8.4375 13.5668 8.71242 14.1562 9.15858V9.14062C14.1562 8.75198 14.4707 8.4375 14.8594 8.4375C15.248 8.4375 15.5625 8.75198 15.5625 9.14062V12.6562C15.5625 13.4315 16.1935 14.0625 16.9688 14.0625C17.744 14.0625 18.375 13.4315 18.375 12.6562C18.375 7.95042 15.261 5.625 12.0469 5.625C8.55736 5.625 5.71875 8.46361 5.71875 11.9531C5.71875 15.4426 8.55736 18.2812 12.0469 18.2812C13.4531 18.2812 14.7832 17.8308 15.8935 16.978C16.6351 16.4118 17.4844 17.5295 16.7504 18.0931C15.3915 19.1361 13.7655 19.6875 12.0469 19.6875C7.78214 19.6875 4.3125 16.2179 4.3125 11.9531C4.3125 7.68839 7.78214 4.21875 12.0469 4.21875C15.9327 4.21875 19.7812 7.08445 19.7812 12.6562C19.7812 14.2074 18.5199 15.4688 16.9688 15.4688Z" fill="#575B7C"></path>
                        </g>
                        <defs>
                            <clipPath id="clip0_220_1613">
                                <rect fill="white" height="24" width="24"></rect>
                            </clipPath>
                        </defs>
                    </svg>
                </x-slot:icon>
            </x-input.text-input>

            <x-input.text-input wire:model="password" type="password" placeholder="Password" :error="$errors->first('password')">
                <x-slot:icon>
                    <svg fill="none" height="20" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.5 9H19V6.99998C19 3.14016 15.8599 0 12 0C8.14012 0 5.00002 3.14016 5.00002 6.99998V9H3.50002C3.43434 8.99996 3.3693 9.01286 3.30862 9.03797C3.24793 9.06309 3.19279 9.09992 3.14636 9.14635C3.09992 9.19279 3.06309 9.24793 3.03797 9.30862C3.01286 9.3693 2.99996 9.43434 3 9.50002V22C3 23.103 3.89695 24 5.00002 24H19C20.103 24 21 23.103 21 22V9.50002C21 9.43434 20.9871 9.3693 20.962 9.30862C20.9369 9.24793 20.9001 9.19279 20.8536 9.14635C20.8072 9.09992 20.7521 9.06309 20.6914 9.03797C20.6307 9.01286 20.5657 8.99996 20.5 9ZM13.4971 19.4448C13.5048 19.5147 13.4977 19.5855 13.4763 19.6524C13.4548 19.7194 13.4195 19.7811 13.3726 19.8335C13.3257 19.8859 13.2683 19.9278 13.2041 19.9565C13.1399 19.9852 13.0704 20 13 20H11C10.9297 20 10.8602 19.9852 10.796 19.9565C10.7318 19.9278 10.6743 19.8859 10.6274 19.8335C10.5805 19.7811 10.5452 19.7194 10.5238 19.6524C10.5023 19.5855 10.4952 19.5147 10.503 19.4448L10.8184 16.6084C10.3062 16.2359 10 15.6465 10 15C10 13.897 10.897 13 12 13C13.1031 13 14.0001 13.8969 14.0001 15C14.0001 15.6465 13.6939 16.2359 13.1817 16.6084L13.4971 19.4448ZM16 9H8.00002V6.99998C8.00002 4.79442 9.79444 3 12 3C14.2056 3 16 4.79442 16 6.99998V9Z" fill="#575B7C"></path>
                    </svg>
                </x-slot:icon>
            </x-input.text-input>
        </div>

        <div class="mt-3">
            <x-custom.turnstile wire:model="turnstileResponse" />
            @error('turnstileResponse')
            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <button type="submit" class="flex h-[50px] justify-center duration-300 items-center gap-3 px-5 py-[15px] rounded-xl text-xs font-bold uppercase bg-[#4046C1] hover:bg-[#5359d8] text-white mt-[22px] w-full">
            Sign Up
        </button>

        <div class="mt-[22px] flex w-full flex-col gap-2.5">
            <x-input.checkbox wire:model="terms" :error="$errors->first('terms')">
                <span class="text-[12px] text-white">I Consent to <a class="text-[#6F7DFF]" href="#">privacy policy, terms of servise</a> and i’m
                            above</span>
            </x-input.checkbox>
        </div>

    </form>

</div>

