<div class="bg-white p-2">
    <div class="sm:block">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <!-- Current: "border-indigo-500 text-indigo-600", Default: "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700" -->
                <button type="button" wire:click="setActiveTab('deposit')" @class([
                    "whitespace-nowrap border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500",
                    "hover:border-gray-300 hover:text-gray-700" => $activeTab !== 'deposit',
                    "border-indigo-500 text-indigo-600" => $activeTab === 'deposit',
                ])>Deposit</button>
                <button type="button" wire:click="setActiveTab('withdraw')" @class([
                    "whitespace-nowrap border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500",
                    "hover:border-gray-300 hover:text-gray-700" => $activeTab !== 'withdraw',
                    "border-indigo-500 text-indigo-600" => $activeTab === 'withdraw',
                ])>Withdraw</button>
            </nav>
        </div>
    </div>
    @if ($activeTab === 'withdraw')
        <x-update-balance.withdraw />
    @else
        <x-update-balance.deposit :depositMethod="$depositMethod" />
    @endif
</div>
