<div>
    <div class="flex items-center gap-2.5 mb-2">
        <span class="text-[#333550]">
            <x-svg-icon icon="users" class="w-4 h-4" />
        </span>
        <h2 class="title text-lg">Users</h2>
    </div>

    <div class="overflow-x-auto max-w-[95vw]">
        @if ($promotedTransactions->isEmpty())
        <div class="p-5 flex items-center justify-center gap-2.5 rounded-2xl border border-dashed border-[#333D4F]">
            <span class="text-xs text-[#62768E]">YOU DON'T HAVE ANY REFERRALS YET</span>
        </div>
        @else
            <table class="!rounded-2xl border-solid border-2 border-[#0B172D] bg-[#060D21] md:min-w-full divide-y divide-gray-800">
                <thead>
                <tr>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm">Referral Code</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm">Amount</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm">Commission</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm">Date</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm">Status</th>
                </tr>
                </thead>
                <tbody class="divide-y divide-gray-900">
                @foreach ($promotedTransactions as $promotedTransaction)
                    <tr wire:key="{{ $promotedTransaction->id }}">
                        <td class="whitespace-nowrap px-3 py-4 text-sm">{{ $promotedTransaction->code }}</td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm">{{ money($promotedTransaction->amount) }}</td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm">{{ money($promotedTransaction->commission) }}</td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm">{{ $promotedTransaction->created_at->format('Y-m-d') }}</td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm">{{ $promotedTransaction->status }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        @endif
    </div>

</div>
