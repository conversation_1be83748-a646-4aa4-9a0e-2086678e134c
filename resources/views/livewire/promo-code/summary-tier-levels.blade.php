<div>
    <div class="flex items-center gap-2.5 mb-2">
        <span class="text-[#333550]">
            <x-svg-icon icon="box" class="w-4 h-4"/>
        </span>
        <h2 class="title text-lg">Tier Levels</h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
        @foreach(\App\Enums\TierLevel::cases() as $tierLevel)
            @if ($tierLevel->level() === 0)
                @continue
            @endif
            <x-profile.referral.tier-level>
                {{ $tierLevel->level() }}

                <x-slot name="conditions">

                    <x-profile.referral.condition-list-item>{{ $tierLevel->commissionRate() * 100 }}% Commission</x-profile.referral.condition-list-item>
                    <x-profile.referral.condition-list-item>{{ $tierLevel->minActiveReferrals() }} Active referrals</x-profile.referral.condition-list-item>
                    <x-profile.referral.condition-list-item>{{ money($tierLevel->depositThreshold()) }} min Commission</x-profile.referral.condition-list-item>
                </x-slot>
            </x-profile.referral.tier-level>
        @endforeach
    </div>

</div>
