<div class="bg-white p-2">
    <div class="sm:mx-auto sm:w-full sm:max-w-sm">
        <h2 class="text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">Payout Commision</h2>
    </div>

    <div class="m-auto mt-3 flex justify-center">
        <span class="isolate inline-flex rounded-md shadow-sm">
          <button type="button" wire:click="selectPayoutType('balance')" @class(["relative inline-flex items-center rounded-l-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-primary focus:z-10", "bg-blue-500" => $payout_type === \App\Enums\PayoutType::BALANCE->value()])>{{ \App\Enums\PayoutType::BALANCE->label() }}</button>
          <button type="button" wire:click="selectPayoutType('crypto')" @class(["relative -ml-px inline-flex items-center rounded-r-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10", "bg-blue-500" => $payout_type === \App\Enums\PayoutType::CRYPTO->value()])>{{ \App\Enums\PayoutType::CRYPTO->label() }}</button>
        </span>
    </div>


    <div class="mt-10 mb-10 sm:mx-auto sm:w-full sm:max-w-sm">
        <p class="text-center">You have <span class="font-semibold">{{ money($available_amount) }}</span> to payout</p>
        <form class="space-y-6" wire:submit="payout">
            @if ($payout_type === \App\Enums\PayoutType::CRYPTO->value())
                <div wire:transition>
                    <div>
                        <label for="crypto_type" class="block text-sm font-medium leading-6 text-gray-900">Crypto Type</label>
                        <select wire:model="selected_crypto_type">
                            @foreach($crypto_types as $crypto_type)
                                <option value="{{ $crypto_type[0] }}">{{ $crypto_type[1] }}</option>
                            @endforeach
                        </select>
                        <x-input-error class="mt-2" :messages="$errors->get('crypto_type')"/>
                    </div>
                    <div>
                        <label for="crypto_address" class="block text-sm font-medium leading-6 text-gray-900">Crypto Address</label>
                        <div class="mt-2">
                            <x-text-input id="crypto_address" wire:model="crypto_address" required class="w-full" />
                        </div>
                        <x-input-error class="mt-2" :messages="$errors->get('crypto_address')"/>
                    </div>
                </div>
            @endif
            <div>
                <label for="amount" class="block text-sm font-medium leading-6 text-gray-900">Amount to payout</label>
                <div class="mt-2">
                    <x-text-input id="amount" wire:model="amount" required class="w-full" />
                </div>
                <x-input-error class="mt-2" :messages="$errors->get('amount')"/>
            </div>

            <div>
                <x-primary-button type="submit" class="w-full text-center">Payout</x-primary-button>
            </div>
        </form>
    </div>
</div>
