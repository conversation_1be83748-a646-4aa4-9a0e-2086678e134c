<div>
    <header>
        <h2 class="text-lg font-medium text-gray-900">
            {{ __('Transaction History') }}
        </h2>

    </header>
    <div class="mt-8 flow-root">
        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                @if (empty($transactions))
                    <p class="text-sm text-gray-500">{{ __('No transactions found.') }}</p>
                @else
                <table class="min-w-full divide-y divide-gray-300">
                    <thead>
                    <tr>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Description</th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Amount</th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Date</th>
                    </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                    @foreach ($transactions as $transaction)
                    <tr wire:key="{{ $transaction->id }}">
                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $transaction->description }}</td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">$ {{ $transaction->public_amount }}</td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $transaction->created_at }}</td>
                    </tr>
                    @endforeach
                    </tbody>
                </table>
                    {{ $transactions->links() }}
                @endif
            </div>
        </div>
    </div>
</div>
