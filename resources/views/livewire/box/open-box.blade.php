<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
        <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
            <div class="max-w-xl">
                <section>
                    <div class="lg:grid lg:grid-cols-2 lg:items-start lg:gap-x-8">
                        <!-- Image gallery -->
                        <div class="flex flex-col-reverse">
                            <div class="aspect-h-1 aspect-w-1 w-full">
                                <!-- Tab panel, show/hide based on tab state. -->
                                <div id="tabs-1-panel-1" aria-labelledby="tabs-1-tab-1" role="tabpanel" tabindex="0">
                                    <img src="{{ $box->getFirstMedia('image')->getTemporaryUrl(\Carbon\Carbon::now()->addMinutes(5), 'thumb') }}" alt="Angled front view with bag zipped and handles upright." class="h-full w-full object-contain object-center sm:rounded-lg">
                                </div>

                                <!-- More images... -->
                            </div>
                        </div>

                        <!-- Product info -->
                        <div class="mt-10 px-4 sm:mt-16 sm:px-0 lg:mt-0">
                            <h1 class="text-3xl font-bold tracking-tight text-gray-900">{{ $box->name }}</h1>

                            <div class="mt-3">
                                <h2 class="sr-only">Product information</h2>
                                <p class="text-3xl tracking-tight text-gray-900">{{ $box->priceMoney }}</p>
                            </div>

                            <div class="mt-6">
                                <h3 class="sr-only">Description</h3>

                                <div class="space-y-6 text-base text-gray-700">
                                    <p>{{ $box->description }}</p>
                                </div>
                            </div>

                            @if ($box->isOpenable())
                            <button wire:click="openBox" type="button" class="inline-flex items-center gap-x-1.5 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                                Unbox
                                <svg class="-mr-0.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                                </svg>
                            </button>
                            @endif

                            @livewire('box.item.item-won')

                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>
