<?php

use Livewire\Attributes\On;
use Livewire\Volt\Component;

new class extends Component {

    public int $countCartItems = 0;

    public function mount(): void
    {
        $this->countCartItems = auth()->user()->cart?->availableItems->count() ?? 0;
    }

    #[On('cart.updated')]
    public function onCartChange(): void
    {
        $this->mount();
    }

}; ?>

<div>
    {{ $countCartItems }}
</div>
