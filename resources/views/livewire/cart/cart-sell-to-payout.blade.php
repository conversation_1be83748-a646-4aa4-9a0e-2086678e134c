<x-modals.confirm :disabled="!auth()->user()->shippable()">
    <x-slot:title>
        Create Payout Request
    </x-slot>

    <div class="flex mt-[25px] flex-col items-start gap-2.5 rounded-[14px] border border-dashed border-[#2D3658] p-[22px] [background:#0F1322]">
        <p class="text-sm font-[325] text-white">
            You are about to create a payout request for the item {{ $cartItem->item->name }}.
        </p>
        <p class="text-sm font-[325] text-white">
            We will process your request and notify you when the payout is complete.
        </p>
    </div>

    <div class="mt-5">
        <x-input.select wire:model="cryptoType" id="cryptoType" :error="$errors->first('cryptoType')" name="cryptoType">
            <option value="" selected>Please Select</option>
            <option disabled>------------</option>
            @foreach($cryptoTypes as $cryptoType)
                <option value="{{ $cryptoType[0] }}">{{ $cryptoType[1] }}</option>
            @endforeach
        </x-input.select>
    </div>
    <div class="mt-5">
        <x-input.text-input wire:model="cryptoAddress" id="cryptoAddress" name="cryptoAddress" placeholder="Crypto Address" :error="$errors->first('cryptoAddress')" />
    </div>
</x-modals.confirm>
