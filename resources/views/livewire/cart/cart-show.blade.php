@php
    if (! isset($scrollTo)) {
        $scrollTo = 'body';
    }

    $scrollIntoViewJsSnippet = ($scrollTo !== false)
        ? <<<JS
           (\$el.closest('{$scrollTo}') || document.querySelector('{$scrollTo}')).scrollIntoView()
        JS
        : '';
@endphp

<div class="flex shrink-0 flex-col rounded-[30px] bg-[linear-gradient(338deg,#11172A_-1.69%,#11172A_85.79%,#3C4E90_140.75%)] p-[22px]">

    <div class="flex justify-between">
        <h2 class="flex items-center gap-2.5">
            <svg fill="none" height="19" viewBox="0 0 19 19" width="19" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.5386 5.74999H1.46164C1.26359 5.74999 1.0923 5.82416 0.947724 5.97262C0.803068 6.121 0.7308 6.2968 0.7308 6.49993V17.7499C0.7308 17.953 0.803068 18.1289 0.947724 18.2772C1.0923 18.4257 1.26359 18.5 1.46164 18.5H17.5386C17.7365 18.5 17.9077 18.4257 18.0524 18.2772C18.1969 18.1289 18.2692 17.953 18.2692 17.7499V6.49993C18.2692 6.29667 18.1972 6.12104 18.0524 5.97262C17.9078 5.82416 17.7365 5.74999 17.5386 5.74999ZM11.4752 9.27729C11.3307 9.42571 11.1593 9.49992 10.9614 9.49992H8.03832C7.84051 9.49992 7.66922 9.42567 7.52452 9.27729C7.37995 9.12882 7.3076 8.95311 7.3076 8.74985C7.3076 8.54675 7.37995 8.37096 7.52452 8.22254C7.66918 8.07407 7.84047 7.99982 8.03832 7.99982H10.9617C11.1594 7.99982 11.3307 8.07407 11.4755 8.22254C11.62 8.37091 11.6923 8.54671 11.6923 8.74985C11.6923 8.95307 11.6199 9.12882 11.4752 9.27729ZM18.7832 0.722632C18.6385 0.574169 18.4674 0.5 18.2694 0.5H0.73076C0.532872 0.5 0.361581 0.574169 0.216964 0.722632C0.0723481 0.871012 0 1.04681 0 1.24995V4.24993C0 4.45303 0.0723481 4.62874 0.217004 4.77725C0.361621 4.92571 0.532873 4.99988 0.7308 4.99988H18.2692C18.467 4.99988 18.6384 4.92571 18.7832 4.77725C18.9277 4.62878 19 4.45307 19 4.24993V1.24995C19 1.04669 18.9277 0.871012 18.7832 0.722632Z" fill="url(#paint0_linear_216_14160)"></path>
                <defs>
                    <linearGradient gradientUnits="userSpaceOnUse" id="paint0_linear_216_14160" x1="9.5" x2="9.5" y1="0.5" y2="18.5">
                        <stop stop-color="#ABADBE"></stop>
                        <stop offset="1" stop-color="white"></stop>
                    </linearGradient>
                </defs>
            </svg>

            <span class="title text-[16px] font-medium uppercase">
                your CART
            </span>
        </h2>

        <div class="ml-auto flex items-center gap-[22px]">
            <div class="flex items-center justify-center gap-2.5 rounded-[12px] bg-[#0E121E] p-4 text-base font-bold text-white">
                <span>Value in Cart: {{ money($totalValue) }}</span>
            </div>

            <button wire:click="close()">
                <svg fill="none" height="23" viewBox="0 0 23 23" width="23" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.8509 11.5L16.9718 7.38877C17.1522 7.20831 17.2536 6.96355 17.2536 6.70835C17.2536 6.45314 17.1522 6.20839 16.9718 6.02793C16.7913 5.84747 16.5466 5.74609 16.2914 5.74609C16.0362 5.74609 15.7914 5.84747 15.611 6.02793L11.4997 10.1488L7.38845 6.02793C7.20799 5.84747 6.96324 5.74609 6.70803 5.74609C6.45283 5.74609 6.20807 5.84747 6.02761 6.02793C5.84716 6.20839 5.74578 6.45314 5.74578 6.70835C5.74578 6.96355 5.84716 7.20831 6.02761 7.38877L10.1484 11.5L6.02761 15.6113C5.93779 15.7004 5.8665 15.8063 5.81784 15.9231C5.76919 16.0399 5.74414 16.1652 5.74414 16.2917C5.74414 16.4182 5.76919 16.5435 5.81784 16.6602C5.8665 16.777 5.93779 16.883 6.02761 16.9721C6.1167 17.0619 6.2227 17.1332 6.33948 17.1819C6.45626 17.2305 6.58152 17.2556 6.70803 17.2556C6.83454 17.2556 6.9598 17.2305 7.07658 17.1819C7.19337 17.1332 7.29936 17.0619 7.38845 16.9721L11.4997 12.8513L15.611 16.9721C15.7 17.0619 15.806 17.1332 15.9228 17.1819C16.0396 17.2305 16.1649 17.2556 16.2914 17.2556C16.4179 17.2556 16.5431 17.2305 16.6599 17.1819C16.7767 17.1332 16.8827 17.0619 16.9718 16.9721C17.0616 16.883 17.1329 16.777 17.1816 16.6602C17.2302 16.5435 17.2553 16.4182 17.2553 16.2917C17.2553 16.1652 17.2302 16.0399 17.1816 15.9231C17.1329 15.8063 17.0616 15.7004 16.9718 15.6113L12.8509 11.5Z" fill="#9A9A9F"></path>
                </svg>
            </button>
        </div>
    </div>

    @if ($items->hasPages())
    <div class="mt-[16px] flex items-center justify-between">
        <span class="text-sm font-bold text-white">Showing  {{  $items->firstItem() }} - {{ $items->lastItem() }} of {{ $items->total() }}</span>

        <div class="flex gap-3">
            @if ($items->onFirstPage())
                <span class="flex h-[39px] items-center justify-center gap-3 rounded-xl bg-[#454666] px-3.5 py-0 text-xs font-normal text-[#D7D9E7] opacity-50">
                        Previous
                </span>
            @else
                @if(method_exists($items,'getCursorName'))
                    <button type="button" dusk="previousPage" wire:key="cursor-{{ $items->getCursorName() }}-{{ $items->previousCursor()->encode() }}" wire:click="setPage('{{$items->previousCursor()->encode()}}','{{ $items->getCursorName() }}')" x-on:click="{{ $scrollIntoViewJsSnippet }}" wire:loading.attr="disabled" class="flex h-[39px] items-center justify-center gap-3 rounded-xl bg-[#454666] px-3.5 py-0 text-xs font-normal text-[#D7D9E7]">
                        Previous
                    </button>
                @else
                    <button
                        type="button" wire:click="previousPage('{{ $items->getPageName() }}')" x-on:click="{{ $scrollIntoViewJsSnippet }}" wire:loading.attr="disabled" dusk="previousPage{{ $items->getPageName() == 'page' ? '' : '.' . $items->getPageName() }}" class="flex h-[39px] items-center justify-center gap-3 rounded-xl bg-[#454666] px-3.5 py-0 text-xs font-normal text-[#D7D9E7]">
                        Previous
                    </button>
                @endif
            @endif
            @if ($items->hasMorePages())
                @if(method_exists($items,'getCursorName'))
                    <button type="button" dusk="nextPage" wire:key="cursor-{{ $items->getCursorName() }}-{{ $items->nextCursor()->encode() }}" wire:click="setPage('{{$items->nextCursor()->encode()}}','{{ $items->getCursorName() }}')" x-on:click="{{ $scrollIntoViewJsSnippet }}" wire:loading.attr="disabled" class="flex h-[39px] items-center justify-center gap-3 rounded-xl bg-[#454666] px-3.5 py-0 text-xs font-normal text-[#D7D9E7]">
                        Next
                    </button>
                @else
                    <button type="button" wire:click="nextPage('{{ $items->getPageName() }}')" x-on:click="{{ $scrollIntoViewJsSnippet }}" wire:loading.attr="disabled" dusk="nextPage{{ $items->getPageName() == 'page' ? '' : '.' . $items->getPageName() }}" class="flex h-[39px] items-center justify-center gap-3 rounded-xl bg-[#454666] px-3.5 py-0 text-xs font-normal text-[#D7D9E7]">
                        Next
                    </button>
                @endif
            @else
                <span class="flex h-[39px] items-center justify-center gap-3 rounded-xl bg-[#454666] px-3.5 py-0 text-xs font-normal text-[#D7D9E7] opacity-50">
                    Next
                </span>
            @endif
        </div>
    </div>
    @endif

    @if ($items->isEmpty())
        <div class="mt-[16px] flex items-center justify-between">
            <h2 class="text-lg font-medium text-white">
                {{ __('You have no items in your cart.') }}
            </h2>
        </div>
    @else
    <div class="mt-[16px] h-[594px] overflow-y-auto">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-[14px]">
            @foreach($items as $item)
                <div class="relative flex flex-col items-center rounded-2xl border-2 border-solid border-[#131F35] bg-[rgba(16,23,43,0.80)] px-[22px] py-[19px]">
                    <img alt="box" class="h-[132px] object-contain" src="{{ $item->getFirstMedia('image')->getTemporaryUrl(\Carbon\Carbon::now()->addMinutes(5), 'thumb') }}">
                    <span class="mt-[6px] text-[15px] font-bold uppercase text-white">{{ money($item->price) }}</span>
                    <span class="mt-[4px] text-[13px] font-bold uppercase text-[#9597B6]">{{ $item->name }}</span>

                    <div class="flex flex-col text-center justify-center space-y-2 mt-2 w-full">
                        <x-buttons.secondary-button wire:modal="cart.cart-sell-to-balance, { cartItemId: {{ $item->pivot->id }} }">Balance</x-buttons.secondary-button>
                        @if ($item->shippable)
                        <x-buttons.secondary-button wire:modal="cart.cart-sell-to-ship, { cartItemId: {{ $item->pivot->id }} }">Ship</x-buttons.secondary-button>
                        @endif
                        <x-buttons.secondary-button wire:modal="cart.cart-sell-to-payout, { cartItemId: {{ $item->pivot->id }} }">Payout</x-buttons.secondary-button>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
    @endif
</div>
