<x-modals.confirm :disabled="!auth()->user()->shippable()">
    <x-slot:title>
        Ship item
    </x-slot>

    <div class="flex mt-[25px] flex-col items-start gap-2.5 rounded-[14px] border border-dashed border-[#2D3658] p-[22px] [background:#0F1322]">
        <p class="text-sm font-[325] text-white">
            Are you sure you want to ship this item {{ $cartItem->item->name }} to your shipping address?
        </p>
        <p class="text-sm font-[325] text-white">
            A shipping request will be created and you will be notified when the item is shipped.
        </p>

        @if (!auth()->user()->shippable())
        <a href="{{ route('profile.index') }}" wire:navigate class="mt-5 text-sm font-[325] text-red-500">
            You need to complete your address before you can ship items.
        </a>
        @endif
    </div>
</x-modals.confirm>
