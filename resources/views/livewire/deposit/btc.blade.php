<?php

use App\Models\PrepaidCard;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rule;
use Livewire\Volt\Component;

new class extends Component {

    public ?float $amount = null;

    /**
     * Mount the component.
     */
    public function mount(): void
    {
    }

    /**
     * Update the profile information for the currently authenticated user.
     */
    public function addsFundsWithBitcoin(): void
    {
        $user = Auth::user();

        $depositWithBitcoin = \App\Actions\Deposit\DepositWithBitcoin::make();

        $validated = $this->validate($depositWithBitcoin->rules());

        $success = $depositWithBitcoin->handle(
            $user,
            $validated['amount']
        );

        if ($success) {
            $this->reset('amount');
        }

        $this->dispatch('balance.updated');
    }

}; ?>

<section>
    <header>
        <h2 class="text-lg font-medium text-gray-900">
            {{ __('Deposit with Bitcoins') }}
        </h2>
    </header>

    <form wire:submit="addsFundsWithBitcoin" class="mt-6 space-y-6">
        <div>
            <x-input-label for="amount" :value="__('Amount')"/>
            <div class="relative mt-2 rounded-md shadow-sm">
                <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <span class="text-gray-500 sm:text-sm">$</span>
                </div>
                <x-text-input wire:model="amount" id="amount" name="amount" type="number" class="pl-7 pr-12 mt-1 block w-full" required
                              autofocus autocomplete="amount" step="0.01"/>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                    <span class="text-gray-500 sm:text-sm" id="price-currency">USD</span>
                </div>
            </div>
            <x-input-error class="mt-2" :messages="$errors->get('amount')"/>
        </div>

        <div class="flex items-center gap-4">
            <x-primary-button>{{ __('Add Funds') }}</x-primary-button>

            <x-action-message class="me-3" on="balance.updated">
                {{ __('Success!') }}
            </x-action-message>
        </div>
    </form>
</section>
