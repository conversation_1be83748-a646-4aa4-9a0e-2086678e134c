<div>
    <div class="flex gap-2 items-center">
        <x-svg-icon icon="faq" class="w-6 h-5" />
        <h2 class="title text-lg">Contact</h2>
    </div>

    <div class="w-full sm:w-3/4 lg:w-1/2 mx-auto p-2">
        <form wire:submit="send">
            <div class="mb-4">
                <x-input.text-input wire:model="email" type="email" placeholder="E-Mail" autofocus autocomplete="username" :error="$errors->first('email')" required>
                    <x-slot:icon>
                        <svg fill="none" height="20" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_220_1613)">
                                <path d="M12.0469 9.84375C10.8837 9.84375 9.9375 10.79 9.9375 11.9531C9.9375 13.1163 10.8837 14.0625 12.0469 14.0625C13.21 14.0625 14.1562 13.1163 14.1562 11.9531C14.1562 10.79 13.21 9.84375 12.0469 9.84375Z" fill="#575B7C"></path>
                                <path d="M11.9531 0C5.36203 0 0 5.36203 0 11.9531C0 18.5442 5.36203 24 11.9531 24C18.5442 24 24 18.5442 24 11.9531C24 5.36203 18.5442 0 11.9531 0ZM16.9688 15.4688C16.0185 15.4688 15.1804 14.9925 14.6711 14.2689C14.0264 14.9986 13.0947 15.4688 12.0469 15.4688C10.1085 15.4688 8.53125 13.8915 8.53125 11.9531C8.53125 10.0147 10.1085 8.4375 12.0469 8.4375C12.8413 8.4375 13.5668 8.71242 14.1562 9.15858V9.14062C14.1562 8.75198 14.4707 8.4375 14.8594 8.4375C15.248 8.4375 15.5625 8.75198 15.5625 9.14062V12.6562C15.5625 13.4315 16.1935 14.0625 16.9688 14.0625C17.744 14.0625 18.375 13.4315 18.375 12.6562C18.375 7.95042 15.261 5.625 12.0469 5.625C8.55736 5.625 5.71875 8.46361 5.71875 11.9531C5.71875 15.4426 8.55736 18.2812 12.0469 18.2812C13.4531 18.2812 14.7832 17.8308 15.8935 16.978C16.6351 16.4118 17.4844 17.5295 16.7504 18.0931C15.3915 19.1361 13.7655 19.6875 12.0469 19.6875C7.78214 19.6875 4.3125 16.2179 4.3125 11.9531C4.3125 7.68839 7.78214 4.21875 12.0469 4.21875C15.9327 4.21875 19.7812 7.08445 19.7812 12.6562C19.7812 14.2074 18.5199 15.4688 16.9688 15.4688Z" fill="#575B7C"></path>
                            </g>
                            <defs>
                                <clipPath id="clip0_220_1613">
                                    <rect fill="white" height="24" width="24"></rect>
                                </clipPath>
                            </defs>
                        </svg>
                    </x-slot:icon>
                </x-input.text-input>
            </div>

            <div class="mb-4">
                <x-input.text-input wire:model="subject" type="text" placeholder="Subject" :error="$errors->first('subject')" required />
            </div>

            <div class="mb-4">
                <x-input.text-area wire:model="content" placeholder="Your message ..." :error="$errors->first('content')" required />
            </div>

            <div class="flex justify-end">
                <x-buttons.base-button type="submit">
                    Send
                </x-buttons.base-button>
            </div>

        </form>
    </div>
</div>
