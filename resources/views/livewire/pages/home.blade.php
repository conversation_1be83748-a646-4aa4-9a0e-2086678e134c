<div>

    <div class="flex flex-col md:flex-row space-x-5 md:h-60">
        <div class="relative w-full md:w-2/3 overflow-hidden rounded-3xl bg-cover px-8 py-6">
            <div class="relative z-[4] flex flex-col">
                <h2 class="flex flex-col text-lg font-bold uppercase">
                    <span class="text-[#ACFF01] [text-shadow:0px_0px_53.2px_#17D107]">JOIN IN RERAIL</span>
                    <span class="font-black text-white">FREE BITCOIN TODAY</span>
                </h2>

                <p class="mt-4 text-sm text-white">

                    Join a revolution in retail <br>  With live events, unique boxes and thousands of products. Online
                    shopping has never been this fun.

                </p>

                @if (!auth()->check())
                <div>
                    <button wire:modal="auth.authentication-modal" class="mt-[18px] flex w-auto font-bold text-xs uppercase shrink-0 items-start md:items-center justify-center gap-2 rounded-xl border-2 border-solid border-[#BCFF00] bg-[rgba(109,176,21,0.30)] px-4 py-3 shadow-[0px_0px_23px_0px_rgba(255,138,0,0.50)]" href="">
                        JOIN THE COMMUNITY
                    </button>
                </div>
                @endif
            </div>

            <img alt="character" class="absolute -bottom-44 right-8 z-[3]" src="{{ Vite::asset('resources/images/banner/character.png') }}">
            <img alt="stones" class="absolute -bottom-0 right-0 z-[2] w-4/5" src="{{ Vite::asset('resources/images/banner/stones.png') }}">
            <img alt="lines" class="h-full absolute inset-0 z-[1] object-cover mix-blend-soft-light" src="{{ Vite::asset('resources/images/banner/lines.png') }}">
            <img alt="bg" class="h-full absolute inset-0 z-0 object-cover opacity-60" src="{{ Vite::asset('resources/images/banner/bg.png') }}">
        </div>

        <div class="hidden w-full md:w-1/3 relative md:flex h-full shrink-0 items-center overflow-hidden rounded-3xl bg-white/20 px-8 py-6">
            <div class="relative z-[4] flex flex-col">
                <div class="flex h-5 w-5 shrink-0 items-center justify-center rounded-full border-[2px] border-[#BCFF00] bg-[#6DB015] fill-[#6DB015] [filter:drop-shadow(0px_0px_23px_rgba(255,138,0,0.65))]">
                    <x-svg-icon icon="arrow-right" class="w-2 h-2" />
                </div>

                <h2 class="mt-[12px] text-lg font-bold text-white">TOP WINNERS <br> FOR THE <br> WEEK</h2>
                <p class="mt-[16px] text-sm text-[#BECEEE]">List of the best players of the week <br> Open cases and become the best!</p>
            </div>

            <img alt="banner bg" class="h-full absolute inset-0 z-0 object-cover" src="{{ Vite::asset('resources/images/banner/2.png') }}">
        </div>
    </div>

    <div class="flex gap-2 items-center mt-5">
        <x-svg-icon icon="box" class="w-6 h-5 opacity-30" />
        <h2 class="title text-lg">
            FEATURED BOXES
        </h2>
        <div class="hidden md:block mx-5 h-9 w-[1px] bg-[#343E59]"></div>
        <a href="{{ route('boxes.index') }}" wire:navigate>
            <x-buttons.base-button class="!text-xs !p-4 !h-7" >
                Show all
            </x-buttons.base-button>
        </a>
    </div>

    <div>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 items-start gap-8">
            @foreach($featuredBoxes as $box)
                <x-boxes.single :box="$box" />
            @endforeach
        </div>
    </div>
</div>
