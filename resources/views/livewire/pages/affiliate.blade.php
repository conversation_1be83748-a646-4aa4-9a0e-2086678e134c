<div>
    <div class="flex gap-2 items-center">
        <x-svg-icon icon="affiliate" class="w-6 h-5 opacity-30" />
        <h2 class="title text-lg">
            Partnerships & Affiliate Program
        </h2>
    </div>

    <div class="mt-5 flex flex-col md:flex-row w-full md:w-4/5 m-auto md:space-x-10 items-center">
        <div class="md:w-1/2 md:text-left">
            <h3 class="text-2xl font-bold text-white">Climb Affiliate Levels</h3>
            <p class="mt-3 text-[#94A8C0]">With MyDrop24 multi-tiered affiliate program, for each
                achieve levels bring greater rewards, with higher commission rates and special perks. Show your promotional prowess and climb from novice to
                elite, unlocking new benefits along the way.
            </p>
        </div>
        <div class="w-full md:w-1/2 mt-8 md:mt-0 flex  justify-center relative">
            <div class="relative">
                <div class="absolute inset-0 rounded-2xl border-4 border-[#5E4AE3] transform translate-x-4 -translate-y-4"></div>

                <img src="{{ Vite::asset('resources/images/affiliate/1.png') }}" alt="Affiliate Level Image" class="rounded-2xl max-w-full relative z-10">
                <!-- Offset Outline -->
            </div>
        </div>
    </div>

    <div class="mt-20 flex md:w-4/5 m-auto flex-col-reverse md:flex-row md:space-x-10 items-center">
        <div class="md:w-1/2 mt-8 md:mt-0 flex justify-center relative">
            <div class="relative">
                <div class="absolute inset-0 rounded-2xl border-4 border-[#5E4AE3] transform -translate-x-1 md:-translate-x-4 -translate-y-4"></div>

                <img src="{{ Vite::asset('resources/images/affiliate/2.png') }}" alt="Affiliate Level Image" class="rounded-2xl max-w-full relative z-10">
                <!-- Offset Outline -->
            </div>
        </div>
        <div class="text-center w-full md:w-1/2 md:text-left">
            <h3 class="text-2xl font-bold text-white">Take full advantages of MyDrop24 Affiliate Program</h3>
            <ul class="mt-4 flex flex-col gap-4">
                <li>
                    <h3 class="font-medium text-sm text-[#C9DDF6]">Analytics &amp; Visual Dashboard</h3>
                    <p class="mt-1 text-xs text-[#8DA1BA]">Optimize your affiliate earnings with a dashboard designed to track and
                        analyze your revenue generation in detail.</p>
                </li>
                <li>
                    <h3 class="font-medium text-sm text-[#C9DDF6]">Manage Multiple Affiliate Links</h3>
                    <p class="mt-1 text-xs text-[#8DA1BA]">Efficiently create, customize, and share up to 4 distinct referral links to
                        gain more audience.</p>
                </li>
                <li>
                    <h3 class="font-medium text-sm text-[#C9DDF6]">Track your all-time Statistics</h3>
                    <p class="mt-1 text-xs text-[#8DA1BA]">Monitor your affiliate progress comprehensively with our advanced charts to
                        help you increase your revenues.</p>
                </li>
            </ul>
        </div>
    </div>

    <div class="mt-[70px] flex h-[131px] w-full shrink-0 items-center justify-between rounded-3xl bg-[linear-gradient(180deg,#060D21_0%,#131A2E_100%)] px-[46px]">
        <div class="relative flex h-full items-center justify-center">
                <span class="relative z-10 bg-gradient-to-b from-[#ABADBE] to-[#ffffff] bg-clip-text text-[23px] font-extrabold uppercase leading-7 [-webkit-text-fill-color:transparent]">
                    EARN NOW
                </span>

            <img alt="affilate" class="absolute left-0 z-0 object-contain blur-sm" src="{{ Vite::asset('resources/images/affiliate/light.png') }}">
        </div>

        @if (auth()->check())
            <a href="{{ route('profile.referral') }}" wire:navigate class="h-[50px] justify-center duration-300 items-center px-5 py-[15px] rounded-xl text-xs font-bold uppercase bg-[#4046C1] hover:bg-[#5359d8] flex gap-3 text-[#D7D9E7]">
                <span>AFFILIATE PROGRAMM</span>
            </a>
        @else
            <button wire:modal="auth.authentication-modal" class="h-[50px] justify-center duration-300 items-center px-5 py-[15px] rounded-xl text-xs font-bold uppercase bg-[#4046C1] hover:bg-[#5359d8] flex gap-3 text-[#D7D9E7]">
                <span>JOIN</span>
            </button>
        @endif
    </div>
</div>

