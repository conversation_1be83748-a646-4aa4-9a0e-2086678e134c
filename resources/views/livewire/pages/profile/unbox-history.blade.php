<div>
    <livewire:profile.navigation />

    <div class="mt-5 overflow-x-auto max-w-[95vw]">
        <h2 class="title text-lg mb-3">
            {{ __('Unbox History') }}
        </h2>

        @if (auth()->user()->unboxHistory->isEmpty())
            <p class="text-sm text-gray-500">{{ __('You did not open any box yet.') }}</p>
        @else
            <table class="!rounded-2xl border-solid border-2 border-[#0B172D] bg-[#060D21] min-w-full divide-y divide-gray-800">
                <thead>
                <tr>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm">Box</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm">Item</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm">Opened At</th>
                </tr>
                </thead>
                <tbody class="divide-y divide-gray-900">
                @foreach ($unboxHistory as $historyEntry)
                    <tr wire:key="{{ $historyEntry->id }}">
                        <td class="whitespace-nowrap px-3 py-4 text-sm">{{ $historyEntry->box_name }}</td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm">{{ $historyEntry->item_name }}</td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm">{{ $historyEntry->created_at->format('Y-m-d H:i') }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
            {{ $unboxHistory->links() }}
        @endif
    </div>

</div>
