<div>
    <livewire:profile.navigation />

    <h2 class="title text-lg mb-3 mt-5">
        {{ __('Referral') }}
    </h2>

    <div class="relative flex flex-col md:flex-row gap-4 justify-between h-fit md:h-[140px] w-full shrink-0 items-center rounded-3xl p-[20px] bg-[linear-gradient(180deg,#060D21_0%,#131A2E_100%)]">
        <div class="md:mr-[51px] flex h-[100px] w-fit items-center gap-4 rounded-2xl p-5 bg-[linear-gradient(180deg,#1F3152_0%,#3568C2_100%)]">
            <div class="flex h-[62px] md:w-[89px] flex-col items-center justify-center rounded-2xl border-[6px] border-solid border-[#142747] px-3.5 py-[15px]">
                <strong class="text-base font-normal text-white">{{ auth()->user()->tierLevel()->level() }}</strong>
                <span class="text-[10px] font-normal uppercase text-white">Level</span>
            </div>

            <div class="flex w-fit flex-col">
                <span class="text-xs font-normal text-white">Total Commission: {{ money($totalCommission) }}</span>
                <span class="text-xs font-normal text-white">Total payoutable Commission: {{ money($totalPayoutAbleCommisions) }}</span>
                <span class="text-xs font-normal text-white">Active users: {{ $activePromotes }}</span>
            </div>
        </div>

        <div>
            <x-buttons.primary-button wire:modal="profile.referrals-payout-commission-modal">
                {{ __('Payout') }}
            </x-buttons.primary-button>
        </div>
    </div>

    <div class="mt-5 flex flex-col lg:flex-row lg:space-x-8">
        <div class="p-4 w-full lg:w-1/3 rounded-lg border-2 border-solid border-[#0B172D] bg-[#060D21]">
            <x-buttons.base-button wire:modal="modals.create-promo-code" class="w-full">
                {{ __('Create Referral Code') }}
            </x-buttons.base-button>

            <h2 class="title text-md mb-3 mt-5">
                {{ __('Referral Codes') }}
            </h2>

            @if ($referralCodes->isEmpty())
            <div>
                <span class="text-gray-500 text-sm">You don't have any referral codes yet.</span>
            </div>
            @else
                @foreach($referralCodes as $referralCode)

                    <div class="flex justify-between space-x-1.5 mb-1" x-data="{
                        promoCode: '{{ \App\Helper\PromoCodeHelper::generateUrl($referralCode) }}',
                        copied: false,
                        timeout: null,
                        copy () {
                            $clipboard(this.promoCode)

                            this.copied = true

                            clearTimeout(this.timeout)

                            this.timeout = setTimeout(() => {
                                this.copied = false
                            }, 3000)
                        }
                    }">
                        <div class="flex items-center">
                            <p class="text-xs font-bold items-center text-gray-400">
                                {{ \App\Helper\PromoCodeHelper::generateUrl($referralCode) }}
                            </p>
                        </div>
                        <x-buttons.base-button class="!p-3 !w-16" x-on:click="copy">
                            <x-svg-icon icon="square-2-stack" class="w-5 h-5 absolute transition-opacity duration-300 ease-in-out"
                                        x-show="!copied"
                                        x-transition:leave="opacity-100"
                                        x-transition:leave-start="opacity-100"
                                        x-transition:leave-end="opacity-0"
                                        x-transition:enter="opacity-0"
                                        x-transition:enter-start="opacity-0"
                                        x-transition:enter-end="opacity-100" />

                            <!-- Check Icon -->
                            <x-svg-icon icon="check" class="w-5 h-5 absolute transition-opacity duration-300 ease-in-out"
                                        x-show="copied"
                                        x-transition:leave="opacity-100"
                                        x-transition:leave-start="opacity-100"
                                        x-transition:leave-end="opacity-0"
                                        x-transition:enter="opacity-0"
                                        x-transition:enter-start="opacity-0"
                                        x-transition:enter-end="opacity-100" />
                        </x-buttons.base-button>
                    </div>
                @endforeach
            @endif
        </div>
        <div class="w-full lg:w-2/3">
            <livewire:promo-code.summary-tier-levels />
        </div>
    </div>
    <div class="w-full mt-5">
        <livewire:promo-code.list-promoted-transactions />
    </div>
</div>
