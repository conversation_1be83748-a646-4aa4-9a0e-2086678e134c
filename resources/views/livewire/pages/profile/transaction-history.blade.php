<div>
    <livewire:profile.navigation />

    <div class="mt-5 overflow-x-auto max-w-[95vw]">
        <h2 class="title text-lg mb-3">
            {{ __('Transaction History') }}
        </h2>

        @if (empty($transactions))
            <p class="text-sm text-gray-500">{{ __('No transactions found.') }}</p>
        @else
            <table class="!rounded-2xl border-solid border-2 border-[#0B172D] bg-[#060D21] min-w-full divide-y divide-gray-800">
                <thead>
                <tr>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm">Description</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm">Amount</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm">Date</th>
                </tr>
                </thead>
                <tbody class="divide-y divide-gray-900">
                @foreach ($transactions as $transaction)
                    <tr wire:key="{{ $transaction->id }}">
                        <td class="whitespace-nowrap px-3 py-4 text-sm">{{ $transaction->description }}</td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm">$ {{ $transaction->public_amount }}</td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm">{{ $transaction->created_at }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
            {{ $transactions->links() }}
        @endif
    </div>

</div>
