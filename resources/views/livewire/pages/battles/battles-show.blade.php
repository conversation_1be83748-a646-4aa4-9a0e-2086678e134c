<div>

    <div class="mt-10 w-full bg-[#060d21] px-6 py-4 border-[1px] border-[#111C30] rounded-3xl">
        <div class="flex h-fit lg:flex-row flex-col gap-4 justify-between items-center">
            <div class="flex flex-col w-full  items-center">
                <div class="flex items-center gap-2 text-sm">
                    <span class="text-[#62768E] font-medium ">Type:</span>
                    <span class="text-[#246BE9] font-bold">{{ $battle->formation->getLabel() }}</span>
                </div>
                <span class="mt-1 px-2 py-0.5 text-xs border-[#111C30] border-[1px] bg-[#0E1527] rounded-lg text-[#E2E6F2] w-fit">{{ $battle->game_type->getLabel() }}</span>
            </div>

            <div class="flex items-center w-full gap-4">
                <!-- Case thumbnails -->
                <div class="hidden lg:flex lg:flex-1">
                    <div class="flex space-x-2">
                        @foreach ($this->alreadyPlayedBoxes->slice(0, 5) as $box)
                            <div class="w-14 h-14 relative opacity-40">
                                <img
                                    src="{{ $box->getFirstMedia('image')->getTemporaryUrl(\Carbon\Carbon::now()->addMinutes(60), 'thumb') }}"
                                    alt="{{ $box->name }}"
                                    class="w-full h-full object-contain"
                                />
                            </div>
                        @endforeach
                    </div>
                </div>


                <!-- Selected case -->
                <div class="flex w-full items-center gap-2 h-fit flex-col ">
                    <div class="w-16 lg:h-16 lg:relative flex flex-col h-fit">
                        <div class="lg:absolute  text-sm font-medium text-[#5F6C87] text-nowrap lg:-top-28 lg:inset-0 flex  flex-col items-center justify-center">
                            <div>
                                Round {{ $this->currentRound }} of {{ $battle->rounds() }}
                            </div>
                            <div>
                                <img src="{{ Vite::asset('resources/images/battle/selector.png') }}" alt="Selector">
                            </div>
                        </div>
                        <img src="{{ $this->currentBox->getFirstMedia('image')->getTemporaryUrl(\Carbon\Carbon::now()->addMinutes(60), 'thumb') }}"
                            alt="{{ $this->currentBox->name }}"
                            class="w-full h-full object-contain"
                        />
                    </div>
                    <div class="flex flex-col text-center">
                        <span class="text-[#E2E6F2] font-bold">BILLIONAIRE</span>
                        <span class="text-[#62768E] text-sm">$50.53</span>
                    </div>
                </div>
            </div>

            <div class="w-full">
                <div class="flex flex-col items-center lg:items-end">
                    <div class="flex items-center lg:flex-row flex-col gap-2 text-sm">
                        <span class="text-[#62768E] font-medium">Battle Value:</span>
                        <span class="text-[#E2E6F2] font-bold">{{ money($this->battleValue) }}</span>
                    </div>
                    <span class="text-[#62768E] text-sm">Fast Open</span>
                </div>
            </diva>
        </div>
    </div>

    <div class="mt-4">
        <x-battles.show.waiting-room :last-drops="$this->lastDrops" :team-values="$this->teamValues" :player-values="$this->playerValues" :players="$players" :battle="$battle" />
    </div>

</div>
