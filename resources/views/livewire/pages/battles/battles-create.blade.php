<div>
    <div class="flex gap-2 items-center">
        <x-svg-icon icon="battles" class="w-6 h-5 opacity-30" />
        <h2 class="title text-lg">CREATE BATTLE</h2>
    </div>

    <div class="flex flex-col lg:grid md:grid-cols-12 gap-8">

        <div class="w-[95vw] lg:w-full flex flex-col  lg:col-span-5 bg-[#060D21] rounded-3xl p-6 overflow-hidden">
            <div class="flex gap-2 justify-between">
                <h2 class="text-xl font-bold text-white">BATTLE SETTINGS</h2>

                <x-input.toggle-double-label wire:model="isPrivate">
                    <x-slot name="leftSide">Public Battle</x-slot>
                    <x-slot name="rightSide">Private Battle</x-slot>
                </x-input.toggle-double-label>
            </div>

            <div class="border-t-[1px] my-5 border-[#111C30]"></div>

            <!-- Game Types -->
            <div class="font-medium text-[#62768E] mt-3 mb-2">Select Game Type</div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <x-buttons.label-button wire:click="updateGameType('normal')" :checked="$selectedGameType === \App\Enums\Battle\GameType::NORMAL">
                    {{ \App\Enums\Battle\GameType::NORMAL->getLabel() }}
                    <x-slot name="description">Standard rules with the player with the highest total unboxed wins</x-slot>
                </x-buttons.label-button>
                <x-buttons.label-button wire:click="updateGameType('shared')" :checked="$selectedGameType === \App\Enums\Battle\GameType::SHARED">
                    {{ \App\Enums\Battle\GameType::SHARED->getLabel() }}
                    <x-slot name="description">The total unboxed value is split between all players</x-slot>
                </x-buttons.label-button>
                <x-buttons.label-button wire:click="updateGameType('cursed')" :checked="$selectedGameType === \App\Enums\Battle\GameType::CURSED">
                    {{ \App\Enums\Battle\GameType::CURSED->getLabel() }}
                    <x-slot name="description">The player with the least unboxed amount wins</x-slot>
                </x-buttons.label-button>
                <x-buttons.label-button wire:click="updateGameType('best_of_last')" :checked="$selectedGameType === \App\Enums\Battle\GameType::BEST_OF_LAST">
                    {{ \App\Enums\Battle\GameType::BEST_OF_LAST->getLabel() }}
                    <x-slot name="description">The player who unboxed the highest value item in the last case wins</x-slot>
                </x-buttons.label-button>
            </div>

            <!-- Game Modes -->
            <div class="flex space-x-4">
                <div>
                    <div class="font-medium text-[#62768E] mt-3 mb-2">Play Alone</div>
                    <div class="flex flex-col xl:flex-row gap-2">
                        <x-buttons.label-button class="py-2"  wire:click="updatePlayerFormation('1v1')" :checked="$selectedPlayerFormation === \App\Enums\Battle\PlayerFormation::ONE_VS_ONE">
                            {{ \App\Enums\Battle\PlayerFormation::ONE_VS_ONE->getLabel() }}
                        </x-buttons.label-button>
                        <x-buttons.label-button class="py-2"  wire:click="updatePlayerFormation('1v1v1')" :checked="$selectedPlayerFormation === \App\Enums\Battle\PlayerFormation::ONE_VS_ONE_VS_ONE">
                            {{ \App\Enums\Battle\PlayerFormation::ONE_VS_ONE_VS_ONE->getLabel() }}
                        </x-buttons.label-button>
                        <x-buttons.label-button class="py-2"  wire:click="updatePlayerFormation('1v1v1v1')" :checked="$selectedPlayerFormation === \App\Enums\Battle\PlayerFormation::ONE_VS_ONE_VS_ONE_VS_ONE">
                            {{ \App\Enums\Battle\PlayerFormation::ONE_VS_ONE_VS_ONE_VS_ONE->getLabel() }}
                        </x-buttons.label-button>
                    </div>
                </div>
                <div>
                    <div class="font-medium text-[#62768E] mt-3 mb-2 text-nowrap">Play as a team</div>
                    <div class="flex gap-4">
                        <x-buttons.label-button class="py-2"  wire:click="updatePlayerFormation('2v2')" :checked="$selectedPlayerFormation === \App\Enums\Battle\PlayerFormation::TWO_VS_TWO">
                            {{ \App\Enums\Battle\PlayerFormation::TWO_VS_TWO->getLabel() }}
                        </x-buttons.label-button>
                    </div>
                </div>
            </div>

            <x-buttons.primary-button wire:click="createBattle" class="mt-4 md:mt-16">
                Create Battle for {{ money($this->totalPrice) }}
            </x-buttons.primary-button>
            <div class="mt-4 text-[13px] text-[#62768E] font-medium">Rounds <span class="text-[#5D7391]">{{ $this->selectedRounds }}/{{ \App\Livewire\Pages\Battles\BattlesCreate::$MAX_ROUNDS }}</span></div>

            <div class="w-full relative">
                <div class="absolute -bottom-[160px] -right-[140px] object-contain">
                    <img class="w-64 h-64" src="{{ Vite::asset('resources/images/battle/box.png') }}" alt="box" />
                </div>
            </div>
        </div>

        <div class="lg:col-span-7">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Add Case Card -->
                <button wire:click="openAddBoxModal()" class="bg-[#060D21] p-6 rounded-[20px] border-[1px] border-[#0B172D] flex flex-col items-center justify-center">
                    <div class="relative w-24 h-24">
                        <x-svg-icon icon="hexagon" class="absolute inset-0 w-full h-full" />
                        <span class="text-2xl absolute inset-0 flex items-center justify-center text-white font-bold">
                            +
                        </span>
                    </div>
                    <p class="text-white text-base font-medium mt-2">Add Case</p>
                </button>

                @foreach($selectedBoxes as $box)
                    <div class="bg-[#060D21] p-3 rounded-[20px] border-[1px] border-[#0B172D] flex flex-col items-center justify-center">
                        <div class="relative">
                            <img src="{{ $box['image_url'] }}" alt="{{ $box['name'] }}" class="mx-auto">
                            <button wire:click="removeBox('{{ $box['id'] }}')" class="absolute top-1 right-1 text-gray-400 hover:text-red-500">
                                <x-lucide-trash-2 class="w-4 text-[#9597B6] opacity-40" />
                            </button>
                        </div>
                        <div class="text-center mt-4">
                            <h3 class="text-xs text-[#9597B6] font-medium uppercase">{{ $box['name'] }}</h3>
                            <p class="text-base font-black mt-2">{{ money($box['price']) }}</p>
                        </div>
                        <div class="flex items-center justify-between mt-4 bg-[#1E1F35] p-2 rounded-md w-10/12">
                            <button wire:click="decreaseAmount('{{ $box['id'] }}')" class="w-8 h-8 bg-[#060D21] text-white font-bold rounded-md hover:bg-blue-500">-</button>
                            <span class="text-white font-bold">{{ $box['selected_amount'] }}</span>
                            <button wire:click="increaseAmount('{{ $box['id'] }}')" class="w-8 h-8 bg-[#060D21] text-white font-bold rounded-md hover:bg-blue-500">+</button>
                        </div>
                    </div>
                @endforeach

            </div>
        </div>

    </div>
</div>
