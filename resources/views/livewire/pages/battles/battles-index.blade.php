<div>
    <div class="flex gap-2 items-center">
        <x-svg-icon icon="battles" class="w-6 h-5 opacity-30" />
        <h2 class="title text-lg">BATTLES</h2>
    </div>

    <div class="relative flex justify-between mt-4 h-32 max-w-[95vw] md:w-full items-center overflow-hidden rounded-3xl bg-[linear-gradient(180deg,#060D21_0%,#131A2E_100%)] pr-[40px]">
        <div class="relative h-full w-60 md:shrink-0">
            <img alt="box" class="absolute left-0 top-0 w-full -translate-x-20 translate-y-4 object-contain" src="{{ Vite::asset('resources/images/battle/box.png') }}">
        </div>

        <a href="{{ route('battles.create') }}" wire:navigate>
            <x-buttons.primary-button>
                <x-lucide-plus class="w5 h-5 mr-2"/>
                Create Battle
            </x-buttons.primary-button>
        </a>
    </div>

    @if ($battles->isEmpty())
        <p class="mt-12 p-4 rounded-3xl border-2 border-solid border-[#0B172D] bg-[#060D21]">
            There are no active battles at the moment.
        </p>
    @else
        <div class="flex flex-col mt-12">
            @foreach($battles as $battle)
                <x-battles.index.row :battle="$battle" />
            @endforeach
        </div>
    @endif
</div>
