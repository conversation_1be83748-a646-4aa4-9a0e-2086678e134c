<div>
    <div class="flex gap-2 items-center">
        <x-svg-icon icon="box" class="w-6 h-5 opacity-30" />
        <h2 class="title text-lg">ALL BOXES</h2>
    </div>

    <div>
        <div class="mt-5 grid grid-cols-12 grid-rows-3 gap-2 lg:flex h-fit lg:h-[78px] w-full shrink-0 items-center rounded-3xl border-2 border-solid border-[#0B172D] bg-[#060D21] px-8 py-[18px]">
            <span class="flex items-center gap-2 row-start-1 col-span-4">
                <x-svg-icon icon="filter" class="w-5 h-5" />

                <span class="text-[13px] uppercase text-white">FILTERS</span>
            </span>

            <span class="row-start-1 col-span-3 lg:hidden"></span>

            <x-buttons.base-button class="row-start-2 col-span-6 lg:mx-3 h-[40px]" wire:click="selectCategory('featured')" :focused="$category === 'featured'">
                FEATURED
            </x-buttons.base-button>

            <x-buttons.base-button class="row-start-2 col-span-6 lg:mr-3 h-[40px]" wire:click="selectCategory('hot')" :focused="$category === 'hot'">
                HOT
            </x-buttons.base-button>

            <x-input.select wire:model.live="sort"
                            class="row-start-3 col-span-12 !w-fit lg:flex cursor-pointer uppercase mr-3 !px-2 h-[40px]  text-xs !bg-[#0E1527] rounded-xl !border-2 border-solid !border-[#15223A] ring-0"
                            >
                <option value="">Recommended</option>
                <option value="price">Price (Low to High)</option>
                <option value="-price">Price (High to Low)</option>
            </x-input.select>

            <button wire:click="resetFilter" class="row-start-1 col-span-5 md:ml-auto flex h-[42px] items-center justify-center gap-2 rounded-xl border-2 border-solid border-[#15223A] bg-[#0E1527] first-letter lg:px-5 py-[15px]">
                <x-svg-icon icon="trash" class="w-4 h-4" />

                <span class="text-[13px] font-bold text-[#E2E6F2]">Clear Filters</span>
            </button>
        </div>
    </div>

    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 items-start">
        @foreach($this->boxes as $box)
            <x-boxes.single :box="$box" />
        @endforeach
    </div>

    <div>
        {{ $this->boxes->links() }}
    </div>
</div>
