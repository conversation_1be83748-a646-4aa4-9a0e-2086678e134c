<div x-data="{ moving: false }">
    <h2 class="flex items-center w-full">
        <div class="flex-grow h-0.5 bg-[linear-gradient(90deg,rgba(1,3,28,0.00)_0%,#222334_100%)]"></div>
        <div class="flex items-center flex-shrink-0">
            <div class="h-[9px] w-[9px] border-2 border-solid border-[#222334] mr-3"></div>
            <span class="title whitespace-nowrap text-[23px] uppercase mx-auto">{{ $box->name }}</span>
            <div class="h-[9px] w-[9px] border-2 border-solid border-[#222334] ml-3"></div>
        </div>
        <div class="flex-grow h-0.5 bg-[linear-gradient(-90deg,rgba(1,3,28,0.00)_0%,#222334_100%)]"></div>
    </h2>

    @if ($items->isEmpty())
    <div>
        <div class="relative mt-[14px] flex h-[283px] w-full shrink-0 items-center justify-center rounded-3xl bg-[linear-gradient(180deg,#04061F_0%,#10122B_100%)]">
            <img alt="left" class="h-[267px]" src="{{ Vite::asset('resources/images/drop-line/left.svg') }}">
            <div class="flex flex-col items-center">
                <img alt="box" class="h-[199px] w-[199px] object-contain" src="{{ $box->getFirstMedia('image')->getTemporaryUrl(\Carbon\Carbon::now()->addMinutes(5), 'thumb') }}">
                @if (false)
                <span class="font-bold text-[#E6CD04]">6.49 $ - NOT ENOUGH 6.49 $</span>
                <span class="text-[#FFFFFF]">Insufficient funds to open the case</span>
                @endif
            </div>
            <img alt="right" class="h-[267px]" src="{{ Vite::asset('resources/images/drop-line/right.svg') }}">
        </div>
    </div>
    @endif

    @if ($items->isNotEmpty())
    <div class="mt-[14px] grid h-[283px] gap-[1px] overflow-hidden rounded-3xl">
        <div class="relative flex h-full shrink-0 items-center justify-center overflow-hidden bg-[linear-gradient(180deg,#04061F_0%,#10122B_100%)]">
            <div class="flex h-full items-center gap-[7px]" x-data="{ show: false }" x-init="$nextTick(() => show = true);">
                @foreach($items as $item)
                    <div>
                        @if ($item->id === $wonItem?->id)
                        <img alt="arrow" class="relative object-contain -bottom-6 z-10 mx-auto" src="{{ Vite::asset('resources/images/battle/arrow.svg') }}">
                        @endif
                        <div class="duration-1000"
                             x-on:item-won.window="show = false; setTimeout(() => show = true, 300)"
                             x-bind:class="{ 'translate-x-0': show, 'translate-x-[3000px]': !show }"
                        >
                            <x-items.single :item="$item" />
                        </div>


                        @if ($item->id === $wonItem?->id)
                        <img alt="arrow" class="relative mx-auto -top-6 z-10 rotate-180 object-contain" src="{{ Vite::asset('resources/images/battle/arrow.svg') }}">
                        @endif
                    </div>
                @endforeach
            </div>

            <div class="absolute left-0 h-full w-40 bg-gradient-to-r from-[#04061F]"></div>
            <div class="absolute right-0 h-full w-40 bg-gradient-to-l from-[#04061F]"></div>
        </div>
    </div>
    @endif

    <div>
        <x-buttons.base-button
            wire:click="openBox"
            class="mx-auto mt-5">
            Pay {{ money($box->price) }}
        </x-buttons.base-button>
    </div>

    <h2 class="mt-[40px] text-center text-base font-normal text-white">DROP IN THE CASE</h2>

    <div class="mt-5 flex flex-wrap gap-5 justify-center">
        @foreach($box->items as $item)
            <x-items.single :item="$item" />
        @endforeach
    </div>

    <div class="flex gap-2 items-center mt-20">
        <x-svg-icon icon="box" class="w-6 h-5 opacity-30" />
        <h2 class="title text-lg">
            RECOMMENDED BOXES
        </h2>
        <div class="hidden md:block mx-5 h-9 w-[1px] bg-[#343E59]"></div>
        <a href="{{ route('boxes.index') }}" wire:navigate>
            <x-buttons.base-button class="!text-xs !p-4 !h-7" >
                Show all
            </x-buttons.base-button>
        </a>
    </div>

    <div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 items-start gap-8">
            @foreach($recommendedBoxes as $box)
                <x-boxes.single :box="$box" />
            @endforeach
        </div>
    </div>
</div>
