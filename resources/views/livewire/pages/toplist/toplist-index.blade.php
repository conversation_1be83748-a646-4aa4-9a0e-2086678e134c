<div>
    <div class="flex gap-2 items-center">
        <x-svg-icon icon="top" class="w-6 h-5 opacity-30" />
        <h2 class="title text-lg">Top Users</h2>
    </div>

    <div class="p-4 md:p-8 space-y-12 md:mt-20">
        <!-- Top 3 Users -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- 1st Place -->
            @foreach($topThree as $unboxHistory)
                <x-top-users.top-user class="{{ $loop->index === 0 ? 'md:order-2 md:mb-14 md:-mt-14' : '' }} {{ $loop->index === 1 ? 'md:order-1' : '' }} {{ $loop->index === 2 ? 'md:order-3' : '' }}" :unboxed="money($unboxHistory->total_item_price)">
                    <x-slot:place>
                        @if ($loop->index === 0)
                            1st
                        @elseif ($loop->index === 1)
                            2nd
                        @elseif ($loop->index === 2)
                            3rd
                        @endif
                    </x-slot:place>
                    <x-slot:avatar>
                        @if ($unboxHistory->user->avatar)
                            <img src="{{ $unboxHistory->user->avatar }}" alt="{{ $unboxHistory->user->name }}" class="h-16 w-16 rounded-full">
                        @else
                            👑
                        @endif
                    </x-slot:avatar>
                    {{ $unboxHistory->user->name }}
                </x-top-users.top-user>
            @endforeach
        </div>

        <!-- Rest of the Leaderboard -->
        <div class="mt-8 flow-root border-2  border-solid border-[#0B172D] bg-[#060D21] rounded-lg p-4">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    <table class="min-w-full divide-y divide-gray-600">
                        <thead class="">
                        <tr>
                            <th scope="col" class="px-3 py-3.5 text-left  font-bold ">
                                Rank
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left font-bold ">
                                User</th>
                            <th scope="col" class="px-3 py-3.5 text-left font-bold ">
                                Unboxed</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($topUsers as $unboxHistory)
                            <tr class="h-8">
                                <td class="text-green-500 font-bold whitespace-nowrap px-3 py-4">{{ $loop->index + 4 }}th</td>
                                <td class="whitespace-nowrap px-3 py-2 ">
                                    <div class=" flex items-center">
                                        <div
                                            class="h-8 w-8 bg-gray-700 rounded-full flex items-center justify-center text-sm mr-2">
                                            @if ($unboxHistory->user->avatar)
                                                <img src="{{ $unboxHistory->user->avatar }}" alt="{{ $unboxHistory->user->name }}" class="h-8 w-8 rounded-full">
                                            @else
                                                🤖
                                            @endif
                                        </div>
                                        <span class="w-fit text-nowrap">{{ $unboxHistory->user->name }}</span>
                                    </div>
                                </td>
                                <td class="text-blue-400  whitespace-nowrap px-3 py-4 ">{{ money($unboxHistory->total_item_price) }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>
