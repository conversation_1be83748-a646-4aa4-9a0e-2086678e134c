<div class="rounded-md bg-[linear-gradient(338deg,#11172A_-1.69%,#11172A_85.79%,#3C4E90_140.75%)] px-[22px] py-[25px]">

    <div class="flex justify-between">
        <div class="gap-[1px] overflow-hidden rounded-[14px]">
            <x-buttons.tab-item disabled="disabled" :is-active="true">Deposit</x-buttons.tab-item>
        </div>
        <div class="flex gap-[1px] overflow-hidden rounded-[14px]">
            <x-buttons.tab-item wire:click="toggleDepositTab()" :is-active="$activeDepositTab === 'prepaid'">Prepaid Card</x-buttons.tab-item>
            <x-buttons.tab-item wire:click="toggleDepositTab()" :is-active="$activeDepositTab === 'crypto'">Crypto</x-buttons.tab-item>
        </div>
    </div>

    @if ($activeDepositTab === 'prepaid')
        <livewire:header.deposit-with-prepaid />
    @endif

</div>
