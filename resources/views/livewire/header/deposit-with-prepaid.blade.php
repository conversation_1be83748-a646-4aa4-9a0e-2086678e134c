<?php

use App\Models\PrepaidCard;
use App\Models\Promo\PromoCode;
use Illuminate\Support\Facades\Auth;
use Livewire\Volt\Component;

new class extends Component {

    public string $code = '';

    public string $promo_code = '';

    /**
     * Mount the component.
     */
    public function mount(): void
    {
        if (\session()->has('promo_code')) {
            $this->promo_code = \session()->get('promo_code');
        }
    }

    /**
     * Update the profile information for the currently authenticated user.
     */
    public function redeemPrepaidCard(): void
    {
        $user = Auth::user();

        $redeemPrepaidCardAction = \App\Actions\PrepaidCard\RedeemPrepaidCard::make();

        $validated = $this->validate($redeemPrepaidCardAction->rules());

        $prepaidCard = PrepaidCard::query()->where('code', $validated['code'])->firstOrFail();

        if ($prepaidCard->isRedeemed()) {
            $this->addError('code', __('This prepaid card has already been redeemed.'));

            return;
        }

        if ($this->promo_code) {
            $promoCode = PromoCode::query()->where('code', $this->promo_code)->first();
        }

        $success = $redeemPrepaidCardAction->handle(
            $user,
            $prepaidCard,
            $promoCode ?? null,
        );

        if ($success) {
            $this->reset('code');
        }

        $this->dispatch('balance.updated');
    }

}; ?>

<section>
    <header>
        <h2 class="text-lg font-medium text-gray-900">
            {{ __('Redeem prepaid card') }}
        </h2>

        <p class="mt-1 text-sm text-gray-600">
            {{ __("Add some funds with your prepaid card") }}
        </p>
    </header>

    <form wire:submit="redeemPrepaidCard" class="mt-6 space-y-6">
        <div>
            <x-input-label for="code" :value="__('Prepaid Card')"/>
            <x-input.text-input wire:model="code" id="code" name="code" type="text" class="mt-1 block w-full" required
                                autofocus autocomplete="code"
                                :error="$errors->first('code')"
            />
        </div>

        <div>
            <x-input-label for="promo_code" :value="__('Referral Code')"/>
            <x-input.text-input wire:model="promo_code" id="promo_code" name="promo_code" type="text"
                                class="mt-1 block w-full"
                                :error="$errors->first('promo_code')"
            />
        </div>

        <div class="flex items-center gap-4">
            <x-primary-button>{{ __('Redeem') }}</x-primary-button>

            <x-action-message class="me-3" on="balance.updated">
                {{ __('Success!') }}
            </x-action-message>
        </div>
    </form>
</section>
