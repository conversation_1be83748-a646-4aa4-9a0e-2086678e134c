<?php

use App\Livewire\Actions\Logout;
use Livewire\Volt\Component;

new class extends Component
{
    /**
     * Log the current user out of the application.
     */
    public function logout(Logout $logout): void
    {
        $logout();

        $this->redirect('/', navigate: true);
    }
}; ?>


<div class="hidden lg:flex gap-6 justify-between items-center font-light">
    <button class="inline-flex items-center justify-center gap-2 rounded-2xl border-2 border-solid border-[#111C30] bg-[#0E1527] px-3 py-1" wire:modal="cart.cart-show">
        <span class="text-center text-xs font-bold text-[#5F6C87]">Cart</span>
        <div class="flex items-center justify-center gap-2.5 rounded-md bg-[rgba(255,255,255,0.05)] px-3 py-1.5 text-center text-xs font-normal text-white">
            <livewire:cart.count />
        </div>
    </button>

    <a class="mr-10 ml-4 flex items-center space-x-6" href="{{ route('profile.index') }}" wire:navigate>
        <livewire:profile.avatar />

        <div class="flex flex-col">
            <span class="text-xs font-medium text-[#C6D0F3]">{{ auth()->user()->name }}</span>
            <strong class="text-xs font-semibold text-[#CCBE8D]">
                <livewire:balance />
            </strong>
        </div>
    </a>

    <div class="flex gap-2">
        <x-buttons.primary-button wire:modal="header.update-balance-modal" class="!px-2">
            <x-svg-icon icon="deposit" class="w-4 h-4" />
        </x-buttons.primary-button>

        <x-buttons.secondary-button wire:click="logout" class="!px-2">
            <x-svg-icon icon="logout" class="w-4 h-4" />
        </x-buttons.secondary-button>
    </div>
</div>
