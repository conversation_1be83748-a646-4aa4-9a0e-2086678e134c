<div
    class="flex h-fit w-full lg:max-h-[580px] flex-col lg:flex-row px-8 py-[46px] text-lightGrey font-medium bg-[#010211]">
    <div class="w-full h-fit flex items-center justify-end">
        <button wire:click="close()"
                class="flex items-center bg-primary rounded-lg border p-1 border-solid border-primary-light hover:bg-opacity-80 disabled:bg-opacity-50  h-[39px] w-[39px] shadow-primarybutton">
            <svg fill="none" viewBox="0 0 23 23" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M12.8509 11.5L16.9718 7.38877C17.1522 7.20831 17.2536 6.96355 17.2536 6.70835C17.2536 6.45314 17.1522 6.20839 16.9718 6.02793C16.7913 5.84747 16.5466 5.74609 16.2914 5.74609C16.0362 5.74609 15.7914 5.84747 15.611 6.02793L11.4997 10.1488L7.38845 6.02793C7.20799 5.84747 6.96324 5.74609 6.70803 5.74609C6.45283 5.74609 6.20807 5.84747 6.02761 6.02793C5.84716 6.20839 5.74578 6.45314 5.74578 6.70835C5.74578 6.96355 5.84716 7.20831 6.02761 7.38877L10.1484 11.5L6.02761 15.6113C5.93779 15.7004 5.8665 15.8063 5.81784 15.9231C5.76919 16.0399 5.74414 16.1652 5.74414 16.2917C5.74414 16.4182 5.76919 16.5435 5.81784 16.6602C5.8665 16.777 5.93779 16.883 6.02761 16.9721C6.1167 17.0619 6.2227 17.1332 6.33948 17.1819C6.45626 17.2305 6.58152 17.2556 6.70803 17.2556C6.83454 17.2556 6.9598 17.2305 7.07658 17.1819C7.19337 17.1332 7.29936 17.0619 7.38845 16.9721L11.4997 12.8513L15.611 16.9721C15.7 17.0619 15.806 17.1332 15.9228 17.1819C16.0396 17.2305 16.1649 17.2556 16.2914 17.2556C16.4179 17.2556 16.5431 17.2305 16.6599 17.1819C16.7767 17.1332 16.8827 17.0619 16.9718 16.9721C17.0616 16.883 17.1329 16.777 17.1816 16.6602C17.2302 16.5435 17.2553 16.4182 17.2553 16.2917C17.2553 16.1652 17.2302 16.0399 17.1816 15.9231C17.1329 15.8063 17.0616 15.7004 16.9718 15.6113L12.8509 11.5Z"
                    fill="white"></path>
            </svg>
        </button>

    </div>

    <ul class="grid grid-cols-2 items-start gap-8 pb-12">
        @foreach ($navigationItemsUp as $item)
            <li class="flex gap-2 justify-start items-center text-[16px] ">
                {!! $item['icon'] !!}
                <a class="truncate" href="{{ $item['path'] }}">{{ $item['text'] }}</a>
            </li>
        @endforeach
    </ul>
    <hr class="border-t border-lightGrey opacity-50 ">
    <ul class="grid grid-cols-2 items-start gap-8 py-12">
        @foreach ($navigationItemsMiddle as $item)
            <li class="flex gap-2 justify-start items-center text-[16px] ">
                {!! $item['icon'] !!}
                <a class="truncate" href="{{ $item['path'] }}">{{ $item['text'] }}</a>
            </li>
        @endforeach
    </ul>
    <hr class="border-t border-lightGrey opacity-50 ">
    <ul class="grid grid-cols-2 items-start gap-8 py-12">
        @foreach ($navigationItemsBottom as $item)
            <li
                class="flex gap-2 justify-start items-center text-[16px] {{ $loop->iteration === 3 ? 'col-span-2' : '' }}">
                {!! $item['icon'] !!}
                <a href="{{ $item['path'] }}">{{ $item['text'] }}</a>
            </li>
        @endforeach
    </ul>

    <hr class="border-t border-lightGrey opacity-50 ">
    <div class="flex justify-center gap-6 items-center py-12">
        <x-sidebar.social-icon>
            <svg fill="none" height="100%" viewBox="0 0 22 20" width="100%" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M8.63229 12.916L8.26837 18.0346C8.78904 18.0346 9.01454 17.811 9.28496 17.5424L11.726 15.2095L16.7842 18.9137C17.7119 19.4307 18.3655 19.1585 18.6157 18.0603L21.9359 2.50263L21.9368 2.50171C22.231 1.13038 21.4409 0.59413 20.537 0.930546L1.02121 8.4023C-0.310709 8.9193 -0.290542 9.6618 0.794791 9.99821L5.78421 11.5501L17.3736 4.29838C17.919 3.93721 18.415 4.13705 18.007 4.49821L8.63229 12.916Z"
                    fill="currentColor"></path>
            </svg>
        </x-sidebar.social-icon>
        <x-sidebar.social-icon>
            <svg fill="none" height="100%" viewBox="0 0 24 18" width="100%" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M5.29569 13.9363C10.5101 15.9712 14.9476 16.3676 19.9763 13.2001C19.9394 13.2554 18.9447 14.7291 16.3289 15.4843C16.8815 16.2396 17.6368 17.1053 17.6368 17.1053C19.3131 17.1053 20.9526 16.6264 22.371 15.7053C23.4763 14.9685 24.1026 13.6974 23.9736 12.3712C23.7526 10.1053 23.2183 7.89482 22.3894 5.77645C21.3394 3.01331 18.8158 1.09749 15.8868 0.821158C15.6289 0.802729 15.4447 0.802729 15.3342 0.802729L15.0395 1.09744C18.4105 2.05535 20.0869 3.54744 20.1237 3.60273C14.9473 0.986916 8.83156 0.950111 3.61837 3.4922C3.61837 3.4922 5.27628 1.85273 8.94203 0.986916L8.72099 0.765869C8.33413 0.765869 7.9657 0.802728 7.57889 0.857963C4.87099 1.31849 2.60523 3.17901 1.6289 5.73953C0.781513 7.95006 0.228896 10.2711 0.0262792 12.629C-0.0842442 13.8816 0.505232 15.1159 1.5368 15.8343C2.89994 16.7369 4.52099 17.2159 6.16046 17.2159C6.16046 17.2159 6.8236 16.3501 7.4868 15.5764C4.99994 14.8395 3.9868 13.3659 3.96837 13.3106L4.43639 13.5496C4.71618 13.6927 5.00298 13.8218 5.29569 13.9363ZM8.1868 12.3343C6.98942 12.2974 6.04994 11.2843 6.0868 10.0685C6.12366 8.92639 7.04471 8.00534 8.1868 7.96849C9.38418 8.00534 10.3237 9.01849 10.2868 10.2343C10.2315 11.3764 9.32889 12.2975 8.1868 12.3343ZM15.7026 12.3343C14.5052 12.2974 13.5657 11.2843 13.6026 10.0685C13.6394 8.92639 14.5605 8.00534 15.7026 7.96849C16.8999 8.00534 17.8394 9.01849 17.8026 10.2343C17.7658 11.3764 16.8446 12.2975 15.7026 12.3343Z"
                    fill="currentColor"></path>
            </svg>
        </x-sidebar.social-icon>
        <x-sidebar.social-icon>
            <svg fill="none" height="100%" viewBox="0 0 12 22" width="100%" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M7.70381 22V11.9655H11.0706L11.5757 8.05372H7.70381V5.55662C7.70381 4.42442 8.01692 3.65284 9.64233 3.65284L11.712 3.65199V0.153153C11.3541 0.10664 10.1255 0 8.69548 0C5.70942 0 3.66511 1.82266 3.66511 5.1692V8.05372H0.288086V11.9655H3.66511V22H7.70381Z"
                    fill="currentColor"></path>
            </svg>
        </x-sidebar.social-icon>
    </div>
</div>
