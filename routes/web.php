<?php

use App\Actions\PrepaidCard\RedeemPrepaidCard;
use App\Http\Controllers\BattleController;
use App\Http\Controllers\BoxController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\PageController;
use App\Http\Middleware\AuthMiddleware;
use App\Livewire\Pages\Affiliate;
use App\Livewire\Pages\Battles\BattlesCreate;
use App\Livewire\Pages\Battles\BattlesIndex;
use App\Livewire\Pages\Battles\BattlesShow;
use App\Livewire\Pages\Boxes\BoxesIndex;
use App\Livewire\Pages\Boxes\BoxesShow;
use App\Livewire\Pages\Contact;
use App\Livewire\Pages\Faq;
use App\Livewire\Pages\Home;
use App\Livewire\Pages\Profile\TransactionHistory;
use App\Livewire\Pages\Profile\UnboxHistory;
use App\Livewire\Pages\Profile\ProfileIndex;
use App\Livewire\Pages\Profile\Referral;
use App\Livewire\Pages\Toplist\ToplistIndex;
use Illuminate\Support\Facades\Route;

Route::get('/', Home::class)
    ->name('home');

Route::get('boxes', BoxesIndex::class)
    ->name('boxes.index');

Route::get('boxes/{box}', BoxesShow::class)
    ->name('boxes.show');

Route::get('battles', BattlesIndex::class)
    ->name('battles.index');

Route::get('toplist', ToplistIndex::class)
    ->name('toplist.index');

Route::get('faq', Faq::class)
    ->name('faq');

Route::get('contact', Contact::class)
    ->name('contact');

Route::get('affiliate', Affiliate::class)
    ->name('affiliate');

Route::get('promo/{code}', [PageController::class, 'promo'])
    ->name('promo');

Route::get('battles/show/{battle}', BattlesShow::class)
    ->name('battles.show');

Route::middleware([AuthMiddleware::class, 'verified'])->group(function () {
    Route::get('balance', [PageController::class, 'balance'])
        ->name('balance');

    Route::post('/prepaid-cards/redeem', RedeemPrepaidCard::class)
        ->name('prepaid-cards.redeem');

    Route::get('profile/index', ProfileIndex::class)
        ->name('profile.index');

    Route::get('profile/transaction-history', TransactionHistory::class)
        ->name('profile.transaction-history');

    Route::get('profile/unbox-history', UnboxHistory::class)
        ->name('profile.unbox-history');

    Route::get('profile/referral', Referral::class)
        ->name('profile.referral');

    Route::view('profile', 'profile')->name('profile');

    //Route::view('profile/affiliate', 'affilixate')
    //    ->name('profile.affiliate');

    Route::view('profile/promo-codes', 'promo-codes')
        ->name('profile.promo-codes');

    Route::get('/cart', [CartController::class, 'show'])
        ->name('cart.show');

    Route::get('/profile/boxes/history', [\App\Http\Controllers\ProfileController::class, 'boxesHistory'])
        ->name('profile.boxes.history');

    Route::get('battles/create', BattlesCreate::class)
        ->name('battles.create');
});

//Route::resource('boxes', BoxController::class)->only(['index', 'show']);

require __DIR__.'/auth.php';
