<?php

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\AuthGoogleController;
use App\Http\Controllers\Api\V1\BoxController;
use App\Http\Controllers\Api\V1\CartController;
use App\Http\Controllers\Api\V1\ItemController;
use App\Http\Controllers\Api\V1\PrepaidCardController;
use App\Http\Controllers\Api\V1\ProfileController;
use App\Http\Controllers\Api\V1\PromoCodeController;
use App\Http\Controllers\Api\V1\UserController;

Route::prefix('/auth')->name('auth.')->group(function () {
    Route::post('/register', [AuthController::class, 'register'])->name('register');
    Route::post('/login', [AuthController::class, 'login'])->name('login');
    Route::post('/forgot-password', [AuthController::class, 'sendPasswordResetLink'])->name('forgot-password');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('reset-password');

    Route::post('/google/token', [AuthGoogleController::class, 'tokenLogin']);
})->middleware('guest');

Route::group(['middleware' => 'auth:sanctum'], function () {
    Route::get('/me', [UserController::class, 'me']);

    Route::prefix('/profile')->name('profile.')->group(function () {
        Route::patch('/', [ProfileController::class, 'update'])->name('update');
        Route::post('/avatar', [ProfileController::class, 'updateAvatar'])->name('update-avatar');
        Route::put('/address', [ProfileController::class, 'updateAddress'])->name('update-address');
        Route::get('/wallet-transactions', [ProfileController::class, 'walletTransactions'])->name('wallet-transactions');
        Route::get('/unbox-history', [ProfileController::class, 'unboxHistory'])->name('unbox-history');
    });

    Route::prefix('/prepaid-cards')->name('prepaid-cards.')->group(function () {
        Route::post('/redeem', [PrepaidCardController::class, 'redeem'])->name('redeem');
    });

    Route::prefix('/boxes')->name('boxes.')->group(function () {
        Route::post('/actions/open', [BoxController::class, 'open'])->name('open');
    });

    Route::prefix('/cart')->name('cart.')->group(function () {
        Route::get('/', [CartController::class, 'show'])->name('show');
        Route::post('/actions/redeem', [CartController::class, 'redeem'])->name('redeem');
    });

    Route::prefix('/promo-codes')->name('promo-codes.')->group(function () {
        Route::get('/', [PromoCodeController::class, 'index'])->name('index');
        Route::post('/', [PromoCodeController::class, 'create'])->name('create');
        Route::get('/promoted-transactions', [PromoCodeController::class, 'promotedTransactions'])->name('promoted-transactions');
        Route::post('/actions/payout', [PromoCodeController::class, 'payout'])->name('payout');
    });
});

Route::prefix('/items')->name('items.')->group(function () {
    Route::get('/{uuid}', [ItemController::class, 'show'])->name('show');
});

Route::prefix('/boxes')->name('boxes.')->group(function () {
    Route::get('/', [BoxController::class, 'index'])->name('index');
    Route::get('/{slug}', [BoxController::class, 'show'])->name('show');
});
